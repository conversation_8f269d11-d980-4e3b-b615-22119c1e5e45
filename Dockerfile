# 使用官方Python镜像作为基础镜像
FROM python:3.13.2-slim

# 设置工作目录
WORKDIR /app

# 安装 Poetry（推荐使用官方安装脚本）
RUN pip install --no-cache-dir poetry==2.1.1

# 复制项目文件（包括 pyproject.toml 和 poetry.lock）
COPY pyproject.toml poetry.lock ./

# 安装项目依赖（使用 --no-root 避免安装当前项目，稍后再复制代码）
RUN poetry config virtualenvs.create false && \
    poetry install --no-interaction --no-ansi --no-root

# 复制项目代码到容器中
COPY . .

# 暴露FastAPI默认端口
EXPOSE 8000

# 启动FastAPI应用
CMD ["uvicorn", "app.main:app", "--host", "0.0.0.0", "--port", "8000"]
