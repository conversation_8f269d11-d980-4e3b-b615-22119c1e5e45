# DevOps Gateway
A repo to contain devops related APIs that provide devops related functions

### Installation
- Install `poetry` if it's not installed yet
  - `curl -sSL https://install.python-poetry.org | python3 -`
  - Add Poetry to your PATH `export PATH="$HOME/.local/bin:$PATH"` 
- Clone the repository
- Install venv
- Install dependencies: `poetry sync --no-root`
- Set up environment variables
- Run the application: `uvicorn app.main:app --reload`

### Usage

### Modules

### Folder Structure
```bash
cmdb-system/
├── .github/                      # GitHub工作流配置
│   └── workflows/
│       ├── ci-cd.yaml
│       └── codeql-analysis.yaml
├── app/                          # 核心应用代码
│   ├── __init__.py
│   ├── main.py                  # FastAPI入口
│   ├── core/                    # 核心配置
│   │   ├── config.py            # 配置管理（Pydantic BaseSettings）
│   │   └── security.py          # 安全模块
│   ├── db/                      # 数据库模块
│   │   ├── session.py           # 数据库会话管理
│   │   └── models/              # SQLAlchemy模型
│   │       ├── asset.py         # 资产基类模型
│   │       ├── server.py        # 物理服务器模型
│   │       ├── aws_resource.py # AWS资源模型
│   │       └── application.py   # 应用服务模型
│   ├── routes/                  # API路由
│   │   ├── v1/                  # API版本控制
│   │   │   ├── __init__.py
│   │   │   ├── assets.py        # 资产通用接口
│   │   │   ├── servers.py       # 物理服务器接口
│   │   │   ├── aws_resources.py # AWS资源接口
│   │   │   └── healthcheck.py   # 健康检查
│   ├── schemas/                 # Pydantic数据模型
│   │   ├── asset.py
│   │   ├── server.py
│   │   └── aws_resource.py
│   ├── services/                # 业务逻辑层
│   │   ├── asset_service.py
│   │   ├── server_service.py
│   │   ├── aws_service.py
│   │   └── discovery.py         # 自动发现服务
│   ├── utils/                   # 工具类
│   │   ├── aws_client.py        # AWS SDK客户端
│   │   └── ssh_client.py        # SSH连接工具
├── tests/                       # 测试套件
│   ├── unit/
│   └── integration/
├── migrations/                  # 数据库迁移脚本（Alembic）
├── scripts/                     # 运维脚本
│   ├── db_init.py               # 数据库初始化
│   ├── asset_sync.py            # 资产同步脚本
│   └── aws_discovery.py         # AWS资源发现
├── requirements/
│   ├── base.txt                 # 基础依赖
│   ├── dev.txt                  # 开发依赖
│   └── prod.txt                 # 生产依赖
├── docs/                        # 文档
│   └── api_docs/               # OpenAPI生成文档
├── .env                         # 环境变量
├── .gitignore
├── pyproject.toml               # Poetry配置
├── Dockerfile
└── README.md
```

### Docker Setup
- Build
```bash
docker build -t devops-gateway-app .
```
- Run
```bash
docker run -itd --restart=always -e ENV=prd -p 8000:8000 --name devops-gateway-app devops-gateway-app
```
