[project]
name = "devops-gateway"
version = "0.1.0"
description = "HS Devops Gateway"
authors = [
    {name = "<PERSON>",email = "<EMAIL>"}
]
readme = "README.md"
requires-python = ">=3.13"
dependencies = [
    "fastapi (>=0.115.11,<0.116.0)",
    "sqlalchemy (>=2.0.39,<3.0.0)",
    "pydantic-settings (>=2.8.1,<3.0.0)",
    "python-dotenv (>=1.0.1,<2.0.0)",
    "uvicorn (>=0.34.0,<0.35.0)",
    "pyyaml (>=6.0.2,<7.0.0)",
    "pymysql (>=1.1.1,<2.0.0)",
    "cryptography (>=44.0.2,<45.0.0)",
    "python-dateutil (>=2.9.0.post0,<3.0.0)",
    "boto3 (>=1.37.25,<2.0.0)",
    "requests (>=2.32.3,<3.0.0)",
    "tenacity (>=9.1.2,<10.0.0)",
    "sendgrid (>=6.11.0,<7.0.0)",
    "paramiko (>=3.5.1,<4.0.0)"
]


[build-system]
requires = ["poetry-core>=2.0.0,<3.0.0"]
build-backend = "poetry.core.masonry.api"

[tool.poetry.group.dev.dependencies]
pytest = "^8.3.5"
httpx = "^0.28.1"
alembic = "^1.15.1"
mypy = "^1.15.0"
types-requests = "^2.32.0.20250306"
