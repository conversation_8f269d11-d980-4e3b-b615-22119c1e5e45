user_facing:
  - name: health_check
    group: health_check
    sli:
      - alert: APIHealthCheckFailed
        annotations:
          description: 'API health check failed: {{ $labels.url }}. MySQL, Redis or MongoDB might be experiencing issues.'
          summary: 'API health check failed: {{ $labels.url }}'
        expr: api_status_value{url="https://housesigma.com/bkv2/api/init/accesstoken/check"} != 1 or absent(api_status_value{url="https://housesigma.com/bkv2/api/init/accesstoken/check"})
        for: 2m
        labels:
          alert_level: P2
          dispatch: devops

      - alert: ITSO_ConsentToAdvertise_ConsentToAdvertise_exist
        annotations:
          description: 'Either ITSO_ConsentToAdvertise or rets.ConsentToAdvertise field exists in the MongoDB documents. <@915440516991176774>'
          summary: 'Either the field ITSO_ConsentToAdvertise or ConsentToAdvertise exists in MongoDB documents. <@915440516991176774>'
        expr: mongo_matching_docs > 0
        for: 0m
        labels:
          alert_level: P5
          dispatch: devops

supporting_software:
  - name: mysql
    group: mysql
    sli:
      - alert: MysqlMasterDown
        annotations:
          description: 'MySQL Master instance is down on {{ $labels.instance }}. This is a critical issue affecting write operations.'
          summary: 'MySQL Master down, instance {{ $labels.instance }}'
        expr: mysql_up == 0 and on(instance) mysql_slave_status_master_server_id == 0
        for: 3m
        labels:
          alert_level: P1
          dispatch: devops

      - alert: MysqlSlaveDown
        annotations:
          description: 'MySQL Slave instance is down on {{ $labels.instance }}. Read operations may be affected.'
          summary: 'MySQL Slave down, instance {{ $labels.instance }}'
        expr: mysql_up == 0 and on(instance) mysql_slave_status_master_server_id > 0
        for: 3m
        labels:
          alert_level: P3
          dispatch: devops

      - alert: MysqlTooManyConnections
        annotations:
          description: 'MySQL too many connections (> 80%), instance {{ $labels.instance }}'
          summary: 'MySQL too many connections (> 80%), instance {{ $labels.instance }}'
        expr: max_over_time(mysql_global_status_threads_connected[1m]) / mysql_global_variables_max_connections * 100 > 80
        for: 2m
        labels:
          alert_level: P4
          dispatch: devops

      - alert: MysqlSlaveIoThreadNotRunning
        annotations:
          description: 'MySQL Slave IO thread not running, instance {{ $labels.instance }}'
          summary: 'MySQL Slave IO thread not running, instance {{ $labels.instance }}'
        expr: ( mysql_slave_status_slave_io_running and ON (instance) mysql_slave_status_master_server_id > 0 ) == 0
        for: 10m
        labels:
          alert_level: P3
          dispatch: devops

      - alert: MysqlSlaveSqlThreadNotRunning
        annotations:
          description: 'MySQL Slave SQL thread not running, instance {{ $labels.instance }}'
          summary: 'MySQL Slave SQL thread not running, instance {{ $labels.instance }}'
        expr: ( mysql_slave_status_slave_sql_running and ON (instance) mysql_slave_status_master_server_id > 0) == 0
        for: 0m
        labels:
          alert_level: P3
          dispatch: devops

      - alert: MysqlSlaveReplicationLag
        annotations:
          description: 'MySQL Slave replication lag, instance {{ $labels.instance }}'
          summary: 'MySQL Slave replication lag, instance {{ $labels.instance }}'
        expr: ( (mysql_slave_status_seconds_behind_master - mysql_slave_status_sql_delay) and ON (instance) mysql_slave_status_master_server_id > 0 ) > 60
        for: 5m
        labels:
          alert_level: P3
          dispatch: devops

      - alert: MysqlHighPreparedStatementsUtilization
        annotations:
          description: 'MySQL high prepared statements utilization (> 80%), instance {{ $labels.instance }}'
          summary: 'MySQL high prepared statements utilization (> 80%), instance {{ $labels.instance }}'
        expr: max_over_time(mysql_global_status_prepared_stmt_count[1m]) / mysql_global_variables_max_prepared_stmt_count * 100 > 80
        for: 2m
        labels:
          alert_level: P5
          dispatch: devops

      - alert: MysqlInnodbLogWaits
        annotations:
          description: 'MySQL InnoDB log waits, instance {{ $labels.instance }}'
          summary: 'MySQL InnoDB log waits, instance {{ $labels.instance }}'
        expr: rate(mysql_global_status_innodb_log_waits[15m]) > 10
        for: 5m
        labels:
          alert_level: P5
          dispatch: devops

      - alert: MysqlRestarted
        annotations:
          description: 'MySQL restarted, instance {{ $labels.instance }}'
          summary: 'MySQL restarted, instance {{ $labels.instance }}'
        expr: delta(mysql_global_status_uptime[15m]) < 0
        for: 0m
        labels:
          alert_level: P5
          dispatch: devops

  - name: mongo
    group: mongo
    sli:
      - alert: MongodbDown
        annotations:
          description: 'MongoDB Down, instance {{ $labels.instance }}'
          summary: 'MongoDB Down, instance {{ $labels.instance }}'
        expr: mongodb_up{instance!~'.*hk.*'} == 0
        for: 5m
        labels:
          alert_level: P3
          dispatch: devops

      - alert: MongodbReplicaMemberUnhealthy
        annotations:
          description: 'Mongodb replica member unhealthy, instance {{ $labels.member_idx }}'
          summary: 'Mongodb replica member unhealthy, instance {{ $labels.member_idx }}'
        expr: min(mongodb_rs_members_health{member_idx!~"^hk.*"}) by (member_idx) == 0
        for: 5m
        labels:
          alert_level: P3
          dispatch: devops

      - alert: MongodbTooManyConnections
        annotations:
          description: 'MongoDB too many connections, instance {{ $labels.instance }}'
          summary: 'MongoDB too many connections, instance {{ $labels.instance }}'
        expr: avg by(instance) (rate(mongodb_ss_connections{conn_type="current"}[1m])) / avg by(instance) (sum(mongodb_ss_connections) by (instance)) * 100 > 80
        for: 5m
        labels:
          alert_level: P4
          dispatch: devops

      - alert: MongodbReplicationLag
        annotations:
          description: 'MongoDB replication lag, instance {{ $labels.instance }}'
          summary: 'MongoDB replication lag, instance {{ $labels.instance }}'
        expr: (mongodb_rs_members_optimeDate{member_state="PRIMARY"} - on (set) group_right mongodb_rs_members_optimeDate{member_state="SECONDARY"}) / 1000 > 10
        for: 10m
        labels:
          alert_level: P4
          dispatch: devops

      - alert: MongodbNumberCursorsOpen
        annotations:
          description: 'MongoDB number cursors open, instance {{ $labels.instance }}'
          summary: 'MongoDB number cursors open, instance {{ $labels.instance }}'
        expr: mongodb_ss_metrics_cursor_open{csr_type="total"} > 10000
        for: 10m
        labels:
          alert_level: P5
          dispatch: devops

      - alert: MongodbCursorsTimeouts
        annotations:
          description: 'MongoDB cursors timeouts, instance {{ $labels.instance }}'
          summary: 'MongoDB cursors timeouts, instance {{ $labels.instance }}'
        expr: increase(mongodb_ss_metrics_cursor_timedOut[1m]) > 100
        for: 10m
        labels:
          alert_level: P5
          dispatch: devops

  - name: redis
    group: redis
    sli:
      - alert: RedisDown
        annotations:
          description: 'Redis down, instance {{ $labels.instance }}'
          summary: 'Redis down, instance {{ $labels.instance }}'
        expr: redis_up == 0
        for: 3m
        labels:
          alert_level: P1
          dispatch: devops

      - alert: RedisConnectionIsZero
        annotations:
          description: 'No connection, instance {{ $labels.instance }}'
          summary: 'No connection, instance {{ $labels.instance }}'
        expr: redis_connected_clients == 0
        for: 10m
        labels:
          alert_level: P1
          dispatch: devops

      - alert: RedisTooManyConnections
        annotations:
          description: 'Redis too many connections, instance {{ $labels.instance }}'
          summary: 'Redis too many connections, instance {{ $labels.instance }}'
        expr: redis_connected_clients / redis_config_maxclients * 100 > 90
        for: 5m
        labels:
          alert_level: P4
          dispatch: devops

      - alert: RedisRejectedConnections
        annotations:
          description: 'Redis rejected connections, instance {{ $labels.instance }}'
          summary: 'Redis rejected connections, instance {{ $labels.instance }}'
        expr: increase(redis_rejected_connections_total[1m]) > 0
        for: 1m
        labels:
          alert_level: P2
          dispatch: devops

      - alert: RedisMissingBackup
        annotations:
          description: 'Redis missing backup, instance {{ $labels.instance }}'
          summary: 'Redis missing backup, instance {{ $labels.instance }}'
        expr: time() - redis_rdb_last_save_timestamp_seconds > 60 * 60 * 24
        for: 0m
        labels:
          alert_level: P4
          dispatch: devops

      - alert: RedisOutOfSystemMemory
        annotations:
          description: 'Redis out of system memory, instance {{ $labels.instance }}'
          summary: 'Redis out of system memory, instance {{ $labels.instance }}'
        expr: redis_memory_used_bytes / redis_total_system_memory_bytes * 100 > 90
        for: 5m
        labels:
          alert_level: P4
          dispatch: devops

      - alert: RedisOutOfConfiguredMaxmemory
        annotations:
          description: 'Redis out of configured maxmemory, instance {{ $labels.instance }}'
          summary: 'Redis out of configured maxmemory, instance {{ $labels.instance }}'
        expr: redis_memory_used_bytes / redis_memory_max_bytes * 100 > 90 and on(instance) redis_memory_max_bytes > 0
        for: 5m
        labels:
          alert_level: P4
          dispatch: devops

  - name: beanstalkd
    group: beanstalkd
    sli:
      - alert: uptime_absent
        annotations:
          description: 'The uptime metric for instance {{ $labels.instance }} is missing. This may indicate that Beanstalkd exporter is not running or unreachable.'
          summary: 'Beanstalkd uptime metric missing for {{ $labels.instance }}'
        expr: absent(uptime{exported_instance="ovh110.fangintel.com:11300"})
        for: 1m
        labels:
          alert_level: P2
          dispatch: devops

      - alert: jobs_ready_high
        annotations:
          description: 'The tube {{ $labels.tube }} has {{ $value }} ready jobs, exceeding the threshold of 4500.'
          summary: 'The tube {{ $labels.tube }} has {{ $value }} ready jobs, exceeding the threshold of 4500.'
        expr: tube_current_jobs_ready{tube="v2_datafeed_llama_production"} > 4500
        for: 1m
        labels:
          alert_level: P4
          dispatch: devops

  - name: elasticsearch
    group: elasticsearch
    sli:
      - alert: ElasticsearchClusterHealthRed
        expr: elasticsearch_cluster_health_status{color="red"} == 1
        for: 5m
        labels:
          alert_level: P2
          dispatch: devops
        annotations:
          summary: 'Elasticsearch cluster is in RED state ({{ $labels.cluster }})'
          description: >
            Elasticsearch cluster "{{ $labels.cluster }}" (instance {{ $labels.instance }}) health is RED for more than 5 minutes.
            Some primary shards are unavailable!
            STATUS = {{ $labels.color }}
      

      - alert: ElasticsearchClusterHealthYellow
        expr: elasticsearch_cluster_health_status{color="yellow"} == 1
        for: 10m
        labels:
          alert_level: P3
          dispatch: devops
        annotations:
          summary: 'Elasticsearch cluster is in YELLOW state ({{ $labels.cluster }})'
          description: >
            Elasticsearch cluster "{{ $labels.cluster }}" (instance {{ $labels.instance }}) health is YELLOW for more than 10 minutes.
            Some replica shards are not allocated.
            STATUS = {{ $labels.color }}
      

      - alert: ElasticsearchShardAllocationFailures
        annotations:
          description: 'Elasticsearch cluster has unassigned shards. Unassigned shards: { { $value } }'
          summary: 'Elasticsearch cluster has unassigned shards ({{ $value }})'
        expr: elasticsearch_cluster_health_unassigned_shards > 0
        for: 10m
        labels:
          alert_level: P3
          dispatch: devops

  - name: nginx
    group: nginx
    sli:
      - alert: NginxServerDown
        annotations:
          description: 'Nginx server DOWN: url: {{ $labels.instance }} host: {{ $labels.cache_host }}.'
          summary: 'Nginx server DOWN: url: {{ $labels.instance }} host: {{ $labels.cache_host }}.'
        expr: probe_success{env="prd"} == 0
        for: 1m
        labels:
          alert_level: P2
          dispatch: devops
      - alert: NginxSuccess95Ratio
        annotations:
          description: 'Nginx success rate for {{ $labels.host }} - {{ $labels.server }} is {{ printf "%.3f" $value }}.'
          summary: 'Nginx success rate for {{ $labels.host }} - {{ $labels.server }} is {{ printf "%.3f" $value }}.'
        expr: (sum by (host, server) (rate(nginx_http_requests_count{status=~"[23].."}[3m]))) / (sum by (host, server) (rate(nginx_http_requests_count[3m])) or vector(0)) < 0.95
        for: 3m
        labels:
          alert_level: P3
          dispatch: devops
      - alert: NginxSuccess90Ratio
        annotations:
          description: 'Nginx success rate for {{ $labels.host }} - {{ $labels.server }} is {{ printf "%.3f" $value }}.'
          summary: 'Nginx success rate for {{ $labels.host }} - {{ $labels.server }} is {{ printf "%.3f" $value }}.'
        expr: (sum by (host, server) (rate(nginx_http_requests_count{status=~"[23].."}[3m]))) / (sum by (host, server) (rate(nginx_http_requests_count[3m])) or vector(0)) < 0.90
        for: 3m
        labels:
          alert_level: P4
          dispatch: devops
      - alert: HighNginxLatencyP80
        annotations:
          description: 'High Nginx latency P80: {{ $value }}s for host {{ $labels.host }}.'
          summary: 'High Nginx latency P80: {{ $value }}s for host {{ $labels.host }}.'
        expr: histogram_quantile(0.8, sum(rate(nginx_http_request_duration_seconds_bucket{status!="404|500|304|499"}[3m])) by (le, host)) > 2
        for: 3m
        labels:
          alert_level: P3
          dispatch: devops

  - name: php-fpm
    group: php-fpm
    sli:
      - alert: PhpFpmListenQueueHigh
        annotations:
          summary: 'PHP-FPM listen queue backlog on {{ $labels.instance }}'
          description: PHP-FPM listen queue has exceeded 10, indicating request queuing due to lack of workers.
        expr: phpfpm_listen_queue > 10
        for: 2m
        labels:
          alert_level: P4
          dispatch: devops

      - alert: PhpFpmIdleProcessesLow
        annotations:
          summary: 'PHP-FPM has no idle processes ({{ $labels.instance }})'
          description: >
            PHP-FPM at {{ $labels.instance }} idle processes low for 2 minutes.
            This may indicate it is under high load and new requests are queuing.
        expr: phpfpm_idle_processes < 5 and phpfpm_total_processes > 0
        for: 2m
        labels:
          alert_level: P4
          dispatch: devops

  - name: abcproxy
    group: abcproxy
    sli:
      - alert: Abcproxy Low Bandwidth Warning
        annotations:
          description: 'Only {{ $value }}GB of bandwidth left.'
          summary: 'abcproxy Low Bandwidth Remaining'
        expr: bandwidth_balance_gb < 2
        for: 0m
        labels:
          alert_level: P3
          dispatch: devops

      - alert: Abcproxy Expiry Date Warning
        annotations:
          description: 'Service will expire in {{ $value }} days.'
          summary: 'abcproxy Service Expiry Warning'
        expr: expiry_days_remaining <= 2
        for: 0m
        labels:
          alert_level: P3
          dispatch: devops

  - name: kafka
    group: kafka
    sli:
      - alert: KafkaBrokerDown
        annotations:
          summary: 'Kafka broker is down ({{ $labels.instance }})'
          description: Kafka Broker is down ({{ $labels.instance }})
        expr: kafka_broker_info == 0
        for: 2m
        labels:
          alert_level: P3
          dispatch: devops

      - alert: KafkaConsumerLag
        annotations:
          summary: Kafka consumer({{$labels.consumergroup}}) lag on (topic {{ $labels.topic }})
          description: "Kafka consumer has a 5 minutes and increasing lag\n  VALUE = {{ $value }}\n  LABELS = {{ $labels }}"
        expr: kafka_consumergroup_lag > 500
        for: 5m
        labels:
          alert_level: P4
          dispatch: devops

      - alert: KafkaUnderReplicatedPartitions
        annotations:
          summary: 'Kafka has under-replicated partitions ({{ $labels.instance }})'
          description: 'Kafka has under-replicated partitions ({{ $labels.instance }})'
        expr: kafka_topic_partition_under_replicated_partition > 0
        for: 5m
        labels:
          alert_level: P4
          dispatch: devops

      - alert: KafkaPartitionReplicaSyncFailure
        annotations:
          summary: 'Kafka partition replica sync failure ({{ $labels.instance }})'
          description: >
            Kafka partition replica sync failure on {{ $labels.instance }}.
            This may cause availability or consistency issues.
        expr: kafka_topic_partition_in_sync_replica == 0
        for: 5m
        labels:
          alert_level: P4
          dispatch: devops

      - alert: KafkaPartitionLeaderDown
        annotations:
          summary: 'Kafka partition has no leader ({{ $labels.instance }})'
          description: >
            One or more partitions on Kafka broker {{ $labels.instance }} do not have a leader.
            These partitions are unavailable for reads/writes.
        expr: kafka_topic_partition_leader == 0
        for: 5m
        labels:
          alert_level: P4
          dispatch: devops


supporting_hardware:
  - name: server
    group: server
    sli:
      - alert: HostIsDown
        annotations:
          description: 'Host is down ({{ $labels.instance }}) for 3 mins'
          summary: 'Host is down ({{ $labels.instance }}) for 3 mins'
        expr: up{job="node", instance!~"^hk.*"} == 0
        for: 3m
        labels:
          alert_level: P0
          dispatch: devops
      - alert: HkHostIsDown
        annotations:
          description: 'Host is down ({{ $labels.instance }}) for 10 mins'
          summary: 'Host is down ({{ $labels.instance }}) for 10 mins'
        expr: up{job="node", instance=~"^hk.*"} == 0
        for: 10m
        labels:
          alert_level: P2
          dispatch: devops
