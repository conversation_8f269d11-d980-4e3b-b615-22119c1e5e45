application:
  - name: health_check
    group: health_check
    sli:
      - alert: APIHealthCheckFailed
        annotations:
          summary: "API health check failed: {{ $labels.url }}"
          description: "API health check failed: {{ $labels.url }}. MySQL, Redis or MongoDB might be experiencing issues."
        expr: api_status_value{url="https://housesigma.com/bkv2/api/init/accesstoken/check"} != 1 or absent(api_status_value{url="https://housesigma.com/bkv2/api/init/accesstoken/check"})
        for: 2m
        labels:
          alert_level: P2
          dispatch: devops

      - alert: HttpStatusCodeNot200
        annotations:
          summary: "HTTP status code is not 200 for {{ $labels.instance }}"
          description: "The HTTP status code for {{ $labels.instance }} has not been 200 for 3 minutes. Current value: {{ $value }}"
        expr: probe_http_status_code{env!="prd"} != 200
        for: 5m
        labels:
          alert_level: P3
          dispatch: devops

      - alert: BlackboxProbeFailed
        annotations:
          summary: "Blackbox probe failed (instance {{ $labels.instance }})"
          description: "<PERSON>be failed\n  VALUE = {{ $value }}\n  LABELS = {{ $labels }}"
        expr: probe_success == 0
        for: 3m
        labels:
          alert_level: P3
          dispatch: devops

      - alert: housesigmaFubPeopleLogIsZero
        annotations:
          summary: "The fub webhook callback health check is failed(zero)"
          description: "The fub webhook callback health check is failed(zero)"
        expr: housesigma_fub_people_log_inc == 0
        for: 0m
        labels:
          alert_level: P3
          dispatch: devops

      - alert: housesigmaFubWebhookEventIsZero
        annotations:
          summary: "The fub webhook callback health check is failed(zero)"
          description: "The fub webhook callback health check is failed(zero)"
        expr: housesigma_fub_webhook_event_inc == 0
        for: 0m
        labels:
          alert_level: P3
          dispatch: devops

      - alert: BlackboxSslCertificateWillExpireSoon
        annotations:
          summary: "Blackbox SSL certificate will expire soon (instance {{ $labels.instance }})"
          description: "SSL certificate expires in less than 7 days\n  VALUE = {{ $value }}\n  LABELS = {{ $labels }}"
        expr: 3 <= round((last_over_time(probe_ssl_earliest_cert_expiry[10m]) - time()) / 86400, 0.1) < 7
        for: 0m
        labels:
          alert_level: P4
          dispatch: devops

      - alert: ITSO_ConsentToAdvertise_ConsentToAdvertise_exist
        annotations:
          summary: "Either the field ITSO_ConsentToAdvertise or ConsentToAdvertise exists in MongoDB documents. <@915440516991176774>"
          description: "Either ITSO_ConsentToAdvertise or rets.ConsentToAdvertise field exists in the MongoDB documents. <@915440516991176774>"
        expr: mongo_matching_docs > 0
        for: 0m
        labels:
          alert_level: P5
          dispatch: devops

middleware:
  - name: mysql
    group: mysql
    sli:
      - alert: MysqlMasterDown
        annotations:
          summary: "MySQL Master down, instance {{ $labels.instance }}"
          description: "MySQL Master instance is down on {{ $labels.instance }}. This is a critical issue affecting write operations."
        expr: mysql_up == 0 and on(instance) mysql_slave_status_master_server_id == 0
        for: 3m
        labels:
          alert_level: P1
          dispatch: devops

      - alert: MysqlSlaveDown
        annotations:
          summary: "MySQL Slave down, instance {{ $labels.instance }}"
          description: "MySQL Slave instance is down on {{ $labels.instance }}. Read operations may be affected."
        expr: mysql_up == 0 and on(instance) mysql_slave_status_master_server_id > 0
        for: 3m
        labels:
          alert_level: P3
          dispatch: devops

      - alert: MysqlTooManyConnections
        annotations:
          summary: "MySQL too many connections (> 80%), instance {{ $labels.instance }}"
          description: "MySQL too many connections (> 80%), instance {{ $labels.instance }}"
        expr: max_over_time(mysql_global_status_threads_connected[1m]) / mysql_global_variables_max_connections * 100 > 80
        for: 2m
        labels:
          alert_level: P4
          dispatch: devops

      - alert: MysqlSlaveIoThreadNotRunning
        annotations:
          summary: "MySQL Slave IO thread not running, instance {{ $labels.instance }}"
          description: "MySQL Slave IO thread not running, instance {{ $labels.instance }}"
        expr: ( mysql_slave_status_slave_io_running and ON (instance) mysql_slave_status_master_server_id > 0 ) == 0
        for: 5m
        labels:
          alert_level: P3
          dispatch: devops

      - alert: MysqlSlaveSqlThreadNotRunning
        annotations:
          summary: "MySQL Slave SQL thread not running, instance {{ $labels.instance }}"
          description: "MySQL Slave SQL thread not running, instance {{ $labels.instance }}"
        expr: ( mysql_slave_status_slave_sql_running and ON (instance) mysql_slave_status_master_server_id > 0) == 0
        for: 5m
        labels:
          alert_level: P3
          dispatch: devops

      - alert: MysqlSlaveReplicationLag
        annotations:
          summary: "MySQL Slave replication lag, instance {{ $labels.instance }}"
          description: "MySQL Slave replication lag, instance {{ $labels.instance }}"
        expr: ( (mysql_slave_status_seconds_behind_master - mysql_slave_status_sql_delay) and ON (instance) mysql_slave_status_master_server_id > 0 ) > 60
        for: 5m
        labels:
          alert_level: P3
          dispatch: devops

      - alert: MysqlHighPreparedStatementsUtilization
        annotations:
          summary: "MySQL high prepared statements utilization (> 80%), instance {{ $labels.instance }}"
          description: "MySQL high prepared statements utilization (> 80%), instance {{ $labels.instance }}"
        expr: max_over_time(mysql_global_status_prepared_stmt_count[1m]) / mysql_global_variables_max_prepared_stmt_count * 100 > 80
        for: 2m
        labels:
          alert_level: P5
          dispatch: devops

      - alert: MysqlInnodbLogWaits
        annotations:
          summary: "MySQL InnoDB log waits, instance {{ $labels.instance }}"
          description: "MySQL InnoDB log waits, instance {{ $labels.instance }}"
        expr: rate(mysql_global_status_innodb_log_waits[15m]) > 10
        for: 5m
        labels:
          alert_level: P5
          dispatch: devops

      - alert: MysqlRestarted
        annotations:
          summary: "MySQL restarted, instance {{ $labels.instance }}"
          description: "MySQL restarted, instance {{ $labels.instance }}"
        expr: delta(mysql_global_status_uptime[15m]) < 0
        for: 0m
        labels:
          alert_level: P5
          dispatch: devops

  - name: mongo
    group: mongo
    sli:
      - alert: MongodbDown
        annotations:
          summary: "MongoDB Down, instance {{ $labels.instance }}"
          description: "MongoDB Down, instance {{ $labels.instance }}"
        expr: mongodb_up{instance!~'.*hk.*'} == 0
        for: 5m
        labels:
          alert_level: P3
          dispatch: devops

      - alert: MongodbReplicaMemberUnhealthy
        annotations:
          summary: "Mongodb replica member unhealthy, instance {{ $labels.member_idx }}"
          description: "Mongodb replica member unhealthy, instance {{ $labels.member_idx }}"
        expr: min(mongodb_rs_members_health{member_idx!~"^hk.*"}) by (member_idx) == 0
        for: 5m
        labels:
          alert_level: P3
          dispatch: devops

      - alert: MongodbTooManyConnections
        annotations:
          summary: "MongoDB too many connections, instance {{ $labels.instance }}"
          description: "MongoDB too many connections, instance {{ $labels.instance }}"
        expr: avg by(instance) (rate(mongodb_ss_connections{conn_type="current"}[1m])) / avg by(instance) (sum(mongodb_ss_connections) by (instance)) * 100 > 80
        for: 5m
        labels:
          alert_level: P4
          dispatch: devops

      - alert: MongodbReplicationLag
        annotations:
          summary: "MongoDB replication lag, instance {{ $labels.instance }}"
          description: "MongoDB replication lag, instance {{ $labels.instance }}"
        expr: (mongodb_rs_members_optimeDate{member_state="PRIMARY"} - on (set) group_right mongodb_rs_members_optimeDate{member_state="SECONDARY"}) / 1000 > 10
        for: 10m
        labels:
          alert_level: P4
          dispatch: devops

      - alert: MongodbNumberCursorsOpen
        annotations:
          summary: "MongoDB number cursors open, instance {{ $labels.instance }}"
          description: "MongoDB number cursors open, instance {{ $labels.instance }}"
        expr: mongodb_ss_metrics_cursor_open{csr_type="total"} > 10000
        for: 10m
        labels:
          alert_level: P5
          dispatch: devops

      - alert: MongodbCursorsTimeouts
        annotations:
          summary: "MongoDB cursors timeouts, instance {{ $labels.instance }}"
          description: "MongoDB cursors timeouts, instance {{ $labels.instance }}"
        expr: increase(mongodb_ss_metrics_cursor_timedOut[1m]) > 100
        for: 10m
        labels:
          alert_level: P5
          dispatch: devops

  - name: redis
    group: redis
    sli:
      - alert: RedisDown
        annotations:
          summary: "Redis down, instance {{ $labels.instance }}"
          description: "Redis down, instance {{ $labels.instance }}"
        expr: redis_up == 0
        for: 3m
        labels:
          alert_level: P1
          dispatch: devops

      - alert: RedisConnectionIsZero
        annotations:
          summary: "No connection, instance {{ $labels.instance }}"
          description: "No connection, instance {{ $labels.instance }}"
        expr: redis_connected_clients == 0
        for: 10m
        labels:
          alert_level: P1
          dispatch: devops

      - alert: RedisTooManyConnections
        annotations:
          summary: "Redis too many connections, instance {{ $labels.instance }}"
          description: "Redis too many connections, instance {{ $labels.instance }}"
        expr: redis_connected_clients / redis_config_maxclients * 100 > 90
        for: 5m
        labels:
          alert_level: P4
          dispatch: devops

      - alert: RedisRejectedConnections
        annotations:
          summary: "Redis rejected connections, instance {{ $labels.instance }}"
          description: "Redis rejected connections, instance {{ $labels.instance }}"
        expr: increase(redis_rejected_connections_total[1m]) > 0
        for: 1m
        labels:
          alert_level: P2
          dispatch: devops

      - alert: RedisMissingBackup
        annotations:
          summary: "Redis missing backup, instance {{ $labels.instance }}"
          description: "Redis missing backup, instance {{ $labels.instance }}"
        expr: time() - redis_rdb_last_save_timestamp_seconds > 60 * 60 * 24
        for: 0m
        labels:
          alert_level: P4
          dispatch: devops

      - alert: RedisOutOfSystemMemory
        annotations:
          summary: "Redis out of system memory, instance {{ $labels.instance }}"
          description: "Redis out of system memory, instance {{ $labels.instance }}"
        expr: redis_memory_used_bytes / redis_total_system_memory_bytes * 100 > 90
        for: 5m
        labels:
          alert_level: P4
          dispatch: devops

      - alert: RedisOutOfConfiguredMaxmemory
        annotations:
          summary: "Redis out of configured maxmemory, instance {{ $labels.instance }}"
          description: "Redis out of configured maxmemory, instance {{ $labels.instance }}"
        expr: redis_memory_used_bytes / redis_memory_max_bytes * 100 > 90 and on(instance) redis_memory_max_bytes > 0
        for: 5m
        labels:
          alert_level: P4
          dispatch: devops

  - name: beanstalkd
    group: beanstalkd
    sli:
      - alert: uptime_absent
        annotations:
          summary: "Beanstalkd uptime metric missing for {{ $labels.instance }}"
          description: "The uptime metric for instance {{ $labels.instance }} is missing. This may indicate that Beanstalkd exporter is not running or unreachable."
        expr: absent(uptime{exported_instance="ovh110.fangintel.com:11300"})
        for: 1m
        labels:
          alert_level: P2
          dispatch: devops

      - alert: jobs_ready_high
        annotations:
          summary: "The tube {{ $labels.tube }} has {{ $value }} ready jobs, exceeding the threshold of 4500."
          description: "The tube {{ $labels.tube }} has {{ $value }} ready jobs, exceeding the threshold of 4500."
        expr: tube_current_jobs_ready{tube="v2_datafeed_llama_production"} > 4500
        for: 1m
        labels:
          alert_level: P4
          dispatch: devops

  - name: elasticsearch
    group: elasticsearch
    sli:
      - alert: ElasticsearchClusterHealthRed
        expr: elasticsearch_cluster_health_status{color="red"} == 1
        for: 5m
        labels:
          alert_level: P2
          dispatch: devops
        annotations:
          summary: "Elasticsearch cluster is in RED state ({{ $labels.cluster }})"
          description: >
            Elasticsearch cluster "{{ $labels.cluster }}" (instance {{ $labels.instance }}) health is RED for more than 5 minutes.
            Some primary shards are unavailable!
            STATUS = {{ $labels.color }}
      

      - alert: ElasticsearchClusterHealthYellow
        expr: elasticsearch_cluster_health_status{color="yellow"} == 1
        for: 10m
        labels:
          alert_level: P3
          dispatch: devops
        annotations:
          summary: "Elasticsearch cluster is in YELLOW state ({{ $labels.cluster }})"
          description: >
            Elasticsearch cluster "{{ $labels.cluster }}" (instance {{ $labels.instance }}) health is YELLOW for more than 10 minutes.
            Some replica shards are not allocated.
            STATUS = {{ $labels.color }}
      

      - alert: ElasticsearchShardAllocationFailures
        annotations:
          summary: "Elasticsearch cluster has unassigned shards ({{ $value }})"
          description: "Elasticsearch cluster has unassigned shards. Unassigned shards: { { $value } }"
        expr: elasticsearch_cluster_health_unassigned_shards > 0
        for: 10m
        labels:
          alert_level: P3
          dispatch: devops

  - name: nginx
    group: nginx
    sli:
      - alert: NginxServerDown
        annotations:
          summary: "Nginx server DOWN: url: {{ $labels.instance }} host: {{ $labels.cache_host }}."
          description: "Nginx server DOWN: url: {{ $labels.instance }} host: {{ $labels.cache_host }}."
        expr: probe_success{env="prd"} == 0
        for: 1m
        labels:
          alert_level: P2
          dispatch: devops
      - alert: NginxSuccess95Ratio
        annotations:
          summary: "Nginx success rate for {{ $labels.host }} - {{ $labels.server }} is {{ printf \"%.3f\" $value }}."
          description: "Nginx success rate for {{ $labels.host }} - {{ $labels.server }} is {{ printf \"%.3f\" $value }}."
        expr: (sum by (host, server) (rate(nginx_http_requests_count{status=~"[23].."}[3m]))) / (sum by (host, server) (rate(nginx_http_requests_count[3m])) or vector(0)) < 0.95
        for: 5m
        labels:
          alert_level: P3
          dispatch: devops
      - alert: NginxSuccess90Ratio
        annotations:
          summary: "Nginx success rate for {{ $labels.host }} - {{ $labels.server }} is {{ printf \"%.3f\" $value }}."
          description: "Nginx success rate for {{ $labels.host }} - {{ $labels.server }} is {{ printf \"%.3f\" $value }}."
        expr: (sum by (host, server) (rate(nginx_http_requests_count{status=~"[23].."}[3m]))) / (sum by (host, server) (rate(nginx_http_requests_count[3m])) or vector(0)) < 0.90
        for: 5m
        labels:
          alert_level: P4
          dispatch: devops
      - alert: HighNginxLatencyP80
        annotations:
          summary: "High Nginx latency P80: {{ $value }}s for host {{ $labels.host }}."
          description: "High Nginx latency P80: {{ $value }}s for host {{ $labels.host }}."
        expr: histogram_quantile(0.8, sum(rate(nginx_http_request_duration_seconds_bucket{status!="404|500|304|499"}[3m])) by (le, host)) > 2
        for: 5m
        labels:
          alert_level: P3
          dispatch: devops

  - name: php-fpm
    group: php-fpm
    sli:
      - alert: PhpFpmListenQueueHigh
        annotations:
          summary: "PHP-FPM listen queue backlog on {{ $labels.instance }}"
          description: "PHP-FPM listen queue has exceeded 10, indicating request queuing due to lack of workers."
        expr: phpfpm_listen_queue > 10
        for: 5m
        labels:
          alert_level: P4
          dispatch: devops

      - alert: PhpFpmIdleProcessesLow
        annotations:
          summary: "PHP-FPM has no idle processes ({{ $labels.instance }})"
          description: >
            PHP-FPM at {{ $labels.instance }} idle processes low for 2 minutes.
            This may indicate it is under high load and new requests are queuing.
        expr: phpfpm_idle_processes < 5 and phpfpm_total_processes > 0
        for: 5m
        labels:
          alert_level: P4
          dispatch: devops

  - name: kafka
    group: kafka
    sli:
      - alert: KafkaBrokerDown
        annotations:
          summary: "Kafka broker is down ({{ $labels.instance }})"
          description: "Kafka Broker is down ({{ $labels.instance }})"
        expr: kafka_broker_info == 0
        for: 2m
        labels:
          alert_level: P3
          dispatch: devops

      - alert: KafkaConsumerLag
        annotations:
          summary: "Kafka consumer({{$labels.consumergroup}}) lag on (topic {{ $labels.topic }})"
          description: "Kafka consumer has a 5 minutes and increasing lag\n  VALUE = {{ $value }}\n  LABELS = {{ $labels }}"
        expr: kafka_consumergroup_lag > 500
        for: 5m
        labels:
          alert_level: P4
          dispatch: devops

      - alert: KafkaUnderReplicatedPartitions
        annotations:
          summary: "Kafka has under-replicated partitions ({{ $labels.instance }})"
          description: "Kafka has under-replicated partitions ({{ $labels.instance }})"
        expr: kafka_topic_partition_under_replicated_partition > 0
        for: 5m
        labels:
          alert_level: P4
          dispatch: devops

      - alert: KafkaPartitionReplicaSyncFailure
        annotations:
          summary: "Kafka partition replica sync failure ({{ $labels.instance }})"
          description: >
            Kafka partition replica sync failure on {{ $labels.instance }}.
            This may cause availability or consistency issues.
        expr: kafka_topic_partition_in_sync_replica == 0
        for: 5m
        labels:
          alert_level: P4
          dispatch: devops

      - alert: KafkaPartitionLeaderDown
        annotations:
          summary: "Kafka partition has no leader ({{ $labels.instance }})"
          description: >
            One or more partitions on Kafka broker {{ $labels.instance }} do not have a leader.
            These partitions are unavailable for reads/writes.
        expr: kafka_topic_partition_leader == 0
        for: 5m
        labels:
          alert_level: P4
          dispatch: devops

  - name: abcproxy
    group: abcproxy
    sli:
      - alert: Abcproxy Low Bandwidth Warning
        annotations:
          summary: "abcproxy Low Bandwidth Remaining"
          description: "Only {{ $value }}GB of bandwidth left."
        expr: bandwidth_balance_gb < 2
        for: 0m
        labels:
          alert_level: P3
          dispatch: devops

      - alert: Abcproxy Expiry Date Warning
        annotations:
          summary: "abcproxy Service Expiry Warning"
          description: "Service will expire in {{ $value }} days."
        expr: expiry_days_remaining <= 2
        for: 0m
        labels:
          alert_level: P3
          dispatch: devops

infrastructure:
  - name: server
    group: server
    sli:
      - alert: HostIsDown
        annotations:
          summary: "Host is down ({{ $labels.instance }}) for 3 mins"
          description: "Host is down ({{ $labels.instance }}) for 3 mins"
        expr: up{job="node", instance!~"^hk.*"} == 0
        for: 3m
        labels:
          alert_level: P2
          dispatch: devops

      - alert: HostHighCpuLoad
        annotations:
          summary: "Host high CPU load ({{ $labels.instance }})"
          description: "CPU load is > 80%\n  VALUE = {{ $value }}\n  LABELS = {{ $labels }}"
        expr: (sum by (instance) (avg by (mode, instance) (rate(node_cpu_seconds_total{mode!="idle"}[2m]))) > 0.8) * on(instance) group_left (nodename) node_uname_info{nodename=~".+"}
        for: 10m
        labels:
          alert_level: P5
          dispatch: devops

      - alert: HostCpuHighIowait
        annotations:
          summary: "Host CPU high iowait ({{ $labels.instance }})"
          description: "CPU iowait > 5%. A high iowait means that you are disk or network bound.\n  VALUE = {{ $value }}\n  LABELS = {{ $labels }}"
        expr: (avg by (instance) (rate(node_cpu_seconds_total{mode="iowait"}[5m])) * 100 > 5) * on(instance) group_left (nodename) node_uname_info{nodename=~".+"}
        for: 5m
        labels:
          alert_level: P5
          dispatch: devops

      - alert: HostOomKillDetected
        annotations:
          summary: "Host OOM kill detected ({{ $labels.instance }})"
          description: "OOM kill detected\n  VALUE = {{ $value }}\n  LABELS = {{ $labels }}"
        expr: (increase(node_vmstat_oom_kill[1m]) > 0) * on(instance) group_left (nodename) node_uname_info{nodename=~".+"}
        for: 0m
        labels:
          alert_level: P4
          dispatch: devops

      - alert: HostSystemdServiceCrashed
        annotations:
          summary: "Host systemd service crashed ({{ $labels.instance }})"
          description: "systemd service crashed\n  VALUE = {{ $value }}\n  LABELS = {{ $labels }}"
        expr: (node_systemd_unit_state{state="failed"} == 1) * on(instance) group_left (nodename) node_uname_info{nodename=~".+"}
        for: 0m
        labels:
          alert_level: P5
          dispatch: devops

      - alert: HostPhysicalComponentTooHot
        annotations:
          summary: "Host physical component too hot ({{ $labels.instance }})"
          description: "Physical hardware component too hot\n  VALUE = {{ $value }}\n  LABELS = {{ $labels }}"
        expr: (node_hwmon_temp_celsius > 75) * on(instance) group_left (nodename) node_uname_info{nodename=~".+"}
        for: 5m
        labels:
          alert_level: P5
          dispatch: devops

      - alert: HostNodeOvertemperatureAlarm
        annotations:
          summary: "Host node overtemperature alarm ({{ $labels.instance }})"
          description: "Physical node temperature alarm triggered\n  VALUE = {{ $value }}\n  LABELS = {{ $labels }}"
        expr: (node_hwmon_temp_crit_alarm_celsius == 1) * on(instance) group_left (nodename) node_uname_info{nodename=~".+"}
        for: 0m
        labels:
          alert_level: P5
          dispatch: devops

      - alert: AwsMskServiceRunningOutOfDiskspace
        annotations:
          summary: "The AWS MSK(instance {{ $labels.instance }}) is running out of diskspace"
          description: "The AWS MSK(instance {{ $labels.instance }}) is running out of diskspace"
        expr: ((node_filesystem_avail_bytes{job="MSK-node-exporter"} * 100) / node_filesystem_size_bytes{job="MSK-node-exporter"}) < 10
        for: 5m
        labels:
          alert_level: P4
          dispatch: devops

      - alert: HostOutOfDiskSpace
        annotations:
          summary: "Host out of disk space ({{ $labels.instance }})"
          description: "Disk is almost full (< 10% left)\n  VALUE = {{ $value }}\n  LABELS = {{ $labels }}"
        expr: ((node_filesystem_avail_bytes * 100) / node_filesystem_size_bytes < 10 and ON (instance, device, mountpoint) node_filesystem_readonly == 0) * on(instance) group_left (nodename) node_uname_info{nodename=~".+"}
        for: 2m
        labels:
          alert_level: P4
          dispatch: devops

      - alert: HostDiskWillFillIn24Hours
        annotations:
          summary: "Host disk will fill in 24 hours ({{ $labels.instance }})"
          description: "Filesystem is predicted to run out of space within the next 24 hours at current write rate\n  VALUE = {{ $value }}\n  LABELS = {{ $labels }}"
        expr: ((node_filesystem_avail_bytes * 100) / node_filesystem_size_bytes < 10 and ON (instance, device, mountpoint) predict_linear(node_filesystem_avail_bytes{fstype!~"tmpfs"}[1h], 24 * 3600) < 0 and ON (instance, device, mountpoint) node_filesystem_readonly == 0) * on(instance) group_left (nodename) node_uname_info{nodename=~".+"}
        for: 2m
        labels:
          alert_level: P4
          dispatch: devops

      - alert: HostOutOfInodes
        annotations:
          summary: "Host out of inodes ({{ $labels.instance }})"
          description: "Disk is almost running out of available inodes (< 10% left)\n  VALUE = {{ $value }}\n  LABELS = {{ $labels }}"
        expr: (node_filesystem_files_free{fstype!="msdosfs"} / node_filesystem_files{fstype!="msdosfs"} * 100 < 10 and ON (instance, device, mountpoint) node_filesystem_readonly == 0) * on(instance) group_left (nodename) node_uname_info{nodename=~".+"}
        for: 2m
        labels:
          alert_level: P4
          dispatch: devops

      - alert: HostInodesWillFillIn24Hours
        annotations:
          summary: "Host inodes will fill in 24 hours ({{ $labels.instance }})"
          description: "Filesystem is predicted to run out of inodes within the next 24 hours at current write rate\n  VALUE = {{ $value }}\n  LABELS = {{ $labels }}"
        expr: (node_filesystem_files_free{fstype!="msdosfs"} / node_filesystem_files{fstype!="msdosfs"} * 100 < 10 and predict_linear(node_filesystem_files_free{fstype!="msdosfs"}[1h], 24 * 3600) < 0 and ON (instance, device, mountpoint) node_filesystem_readonly{fstype!="msdosfs"} == 0) * on(instance) group_left (nodename) node_uname_info{nodename=~".+"}
        for: 2m
        labels:
          alert_level: P4
          dispatch: devops

      - alert: HostUnusualDiskReadRate
        annotations:
          summary: "Host unusual disk read rate ({{ $labels.instance }})"
          description: "Disk is probably reading too much data (> 450 MB/s)\n  VALUE = {{ $value }}\n  LABELS = {{ $labels }}"
        expr: (sum by (instance) (rate(node_disk_read_bytes_total[2m])) / 1024 / 1024 > 450) * on(instance) group_left (nodename) node_uname_info{nodename=~".+"}
        for: 25m
        labels:
          alert_level: P5
          dispatch: devops

      - alert: HostUnusualDiskWriteRate
        annotations:
          summary: "Host unusual disk write rate ({{ $labels.instance }})"
          description: "Disk is probably writing too much data (> 250 MB/s)\n  VALUE = {{ $value }}\n  LABELS = {{ $labels }}"
        expr: (sum by (instance) (rate(node_disk_written_bytes_total[2m])) / 1024 / 1024 > 250) * on(instance) group_left (nodename) node_uname_info{nodename=~".+"}
        for: 40m
        labels:
          alert_level: P5
          dispatch: devops

      - alert: HostUnusualDiskReadLatency
        annotations:
          summary: "Host unusual disk read latency ({{ $labels.instance }})"
          description: "Disk latency is growing (read operations > 100ms)\n  VALUE = {{ $value }}\n  LABELS = {{ $labels }}"
        expr: (rate(node_disk_read_time_seconds_total[1m]) / rate(node_disk_reads_completed_total[1m]) > 0.1 and rate(node_disk_reads_completed_total[1m]) > 0) * on(instance) group_left (nodename) node_uname_info{nodename=~".+"}
        for: 30m
        labels:
          alert_level: P5
          dispatch: devops

      - alert: HostUnusualDiskWriteLatency
        annotations:
          summary: "Host unusual disk write latency ({{ $labels.instance }})"
          description: "Disk latency is growing (write operations > 100ms)\n  VALUE = {{ $value }}\n  LABELS = {{ $labels }}"
        expr: (rate(node_disk_write_time_seconds_total[1m]) / rate(node_disk_writes_completed_total[1m]) > 0.1 and rate(node_disk_writes_completed_total[1m]) > 0) * on(instance) group_left (nodename) node_uname_info{nodename=~".+"}
        for: 30m
        labels:
          alert_level: P5
          dispatch: devops

      - alert: HostFilesystemDeviceError
        annotations:
          summary: "Host filesystem device error ({{ $labels.instance }})"
          description: "{{ $labels.instance }}: Device error with the {{ $labels.mountpoint }} filesystem\n  VALUE = {{ $value }}\n  LABELS = {{ $labels }}"
        expr: node_filesystem_device_error == 1
        for: 2m
        labels:
          alert_level: P3
          dispatch: devops

      - alert: HostRaidArrayGotInactive
        annotations:
          summary: "Host RAID array got inactive ({{ $labels.instance }})"
          description: "RAID array {{ $labels.device }} is in degraded state due to one or more disks failures. Number of spare drives is insufficient to fix issue automatically.\n  VALUE = {{ $value }}\n  LABELS = {{ $labels }}"
        expr: (node_md_state{state="inactive"} > 0) * on(instance) group_left (nodename) node_uname_info{nodename=~".+"}
        for: 0m
        labels:
          alert_level: P3
          dispatch: devops

      - alert: HostRaidDiskFailure
        annotations:
          summary: "Host RAID disk failure ({{ $labels.instance }})"
          description: "At least one device in RAID array on {{ $labels.instance }} failed. Array {{ $labels.md_device }} needs attention and possibly a disk swap\n  VALUE = {{ $value }}\n  LABELS = {{ $labels }}"
        expr: (node_md_disks{state="failed"} > 0) * on(instance) group_left (nodename) node_uname_info{nodename=~".+"}
        for: 2m
        labels:
          alert_level: P3
          dispatch: devops

      - alert: HostOutOfMemory
        annotations:
          summary: "Host out of memory ({{ $labels.instance }})"
          description: "Node memory is filling up (< 10% left)\n  VALUE = {{ $value }}\n  LABELS = {{ $labels }}"
        expr: (node_memory_MemAvailable_bytes / node_memory_MemTotal_bytes * 100 < 10) * on(instance) group_left (nodename) node_uname_info{nodename!="hk20", nodename=~".+"}
        for: 2m
        labels:
          alert_level: P4
          dispatch: devops

      - alert: HostUnusualNetworkThroughputIn
        annotations:
          summary: "Host unusual network throughput in ({{ $labels.instance }})"
          description: "Host network interfaces are probably receiving too much data (> 150 MB/s)\n  VALUE = {{ $value }}\n  LABELS = {{ $labels }}"
        expr: (sum by (instance) (rate(node_network_receive_bytes_total[2m])) / 1024 / 1024 > 150) * on(instance) group_left (nodename) node_uname_info{nodename=~".+"}
        for: 20m
        labels:
          alert_level: P5
          dispatch: devops

      - alert: HostUnusualNetworkThroughputOut
        annotations:
          summary: "Host unusual network throughput out ({{ $labels.instance }})"
          description: "Host network interfaces are probably sending too much data (> 150 MB/s)\n  VALUE = {{ $value }}\n  LABELS = {{ $labels }}"
        expr: (sum by (instance) (rate(node_network_transmit_bytes_total[2m])) / 1024 / 1024 > 150) * on(instance) group_left (nodename) node_uname_info{nodename=~".+"}
        for: 20m
        labels:
          alert_level: P5
          dispatch: devops

      - alert: HostNetworkInterfaceSaturated
        annotations:
          summary: "Host Network Interface Saturated ({{ $labels.instance }})"
          description: "The network interface \"{{ $labels.device }}\" on \"{{ $labels.instance }}\" is getting overloaded.\n  VALUE = {{ $value }}\n  LABELS = {{ $labels }}"
        expr: ((rate(node_network_receive_bytes_total{device!~"^tap.*|^vnet.*|^veth.*|^tun.*"}[1m]) + rate(node_network_transmit_bytes_total{device!~"^tap.*|^vnet.*|^veth.*|^tun.*"}[1m])) / node_network_speed_bytes{device!~"^tap.*|^vnet.*|^veth.*|^tun.*"} > 0.8 < 10000) * on(instance) group_left (nodename) node_uname_info{nodename=~".+"}
        for: 1m
        labels:
          alert_level: P5
          dispatch: devops

      - alert: HostNetworkReceiveErrors
        annotations:
          summary: "Host Network Receive Errors ({{ $labels.instance }})"
          description: "Host {{ $labels.instance }} interface {{ $labels.device }} has encountered {{ printf \"%.0f\" $value }} receive errors in the last two minutes.\n  VALUE = {{ $value }}\n  LABELS = {{ $labels }}"
        expr: (rate(node_network_receive_errs_total[2m]) / rate(node_network_receive_packets_total[2m]) > 0.01) * on(instance) group_left (nodename) node_uname_info{nodename=~".+"}
        for: 2m
        labels:
          alert_level: P5
          dispatch: devops

      - alert: HostNetworkTransmitErrors
        annotations:
          summary: "Host Network Transmit Errors ({{ $labels.instance }})"
          description: "Host {{ $labels.instance }} interface {{ $labels.device }} has encountered {{ printf \"%.0f\" $value }} transmit errors in the last two minutes.\n  VALUE = {{ $value }}\n  LABELS = {{ $labels }}"
        expr: (rate(node_network_transmit_errs_total[2m]) / rate(node_network_transmit_packets_total[2m]) > 0.01) * on(instance) group_left (nodename) node_uname_info{nodename=~".+"}
        for: 2m
        labels:
          alert_level: P5
          dispatch: devops

      - alert: HostNetworkBondDegraded
        annotations:
          summary: "Host Network Bond Degraded ({{ $labels.instance }})"
          description: "Bond \"{{ $labels.device }}\" degraded on \"{{ $labels.instance }}\".\n  VALUE = {{ $value }}\n  LABELS = {{ $labels }}"
        expr: '((node_bonding_active - node_bonding_slaves) != 0) * on(instance) group_left (nodename) node_uname_info{nodename=~".+"}'
        for: 2m
        labels:
          alert_level: P4
          dispatch: devops

      - alert: HostConntrackLimit
        annotations:
          summary: "Host conntrack limit ({{ $labels.instance }})"
          description: "The number of conntrack is approaching limit\n  VALUE = {{ $value }}\n  LABELS = {{ $labels }}"
        expr: (node_nf_conntrack_entries / node_nf_conntrack_entries_limit > 0.8) * on(instance) group_left (nodename) node_uname_info{nodename=~".+"}
        for: 5m
        labels:
          alert_level: P4
          dispatch: devops

      - alert: HostEdacCorrectableErrorsDetected
        annotations:
          summary: "Host EDAC Correctable Errors detected ({{ $labels.instance }})"
          description: "Host {{ $labels.instance }} has had {{ printf \"%.0f\" $value }} correctable memory errors reported by EDAC in the last 5 minutes.\n  VALUE = {{ $value }}\n  LABELS = {{ $labels }}"
        expr: (increase(node_edac_correctable_errors_total[5m]) > 0) * on(instance) group_left (nodename) node_uname_info{nodename=~".+"}
        for: 0m
        labels:
          alert_level: P5
          dispatch: devops

      - alert: HostEdacUncorrectableErrorsDetected
        annotations:
          summary: "Host EDAC Uncorrectable Errors detected ({{ $labels.instance }})"
          description: "Host {{ $labels.instance }} has had {{ printf \"%.0f\" $value }} uncorrectable memory errors reported by EDAC in the last 5 minutes.\n  VALUE = {{ $value }}\n  LABELS = {{ $labels }}"
        expr: (node_edac_uncorrectable_errors_total > 0) * on(instance) group_left (nodename) node_uname_info{nodename=~".+"}
        for: 0m
        labels:
          alert_level: P5
          dispatch: devops

      - alert: HostClockSkew
        annotations:
          summary: "Host clock skew ({{ $labels.instance }})"
          description: "Clock skew detected. Clock is out of sync. Ensure NTP is configured correctly on this host.\n  VALUE = {{ $value }}\n  LABELS = {{ $labels }}"
        expr: ((node_timex_offset_seconds > 0.05 and deriv(node_timex_offset_seconds[5m]) >= 0) or (node_timex_offset_seconds < -0.05 and deriv(node_timex_offset_seconds[5m]) <= 0)) * on(instance) group_left (nodename) node_uname_info{nodename=~".+"}
        for: 10m
        labels:
          alert_level: P5
          dispatch: devops

      - alert: HostClockNotSynchronising
        annotations:
          summary: "Host clock not synchronising ({{ $labels.instance }})"
          description: "Clock not synchronising. Clock is not synchronising. Ensure NTP is configured on this host.\n  VALUE = {{ $value }}\n  LABELS = {{ $labels }}"
        expr: (min_over_time(node_timex_sync_status[1m]) == 0 and node_timex_maxerror_seconds >= 16) * on(instance) group_left (nodename) node_uname_info{nodename=~".+"}
        for: 2m
        labels:
          alert_level: P5
          dispatch: devops

      - alert: HostRequiresReboot
        annotations:
          summary: "Host requires reboot ({{ $labels.instance }})"
          description: "{{ $labels.instance }} requires a reboot.\n  VALUE = {{ $value }}\n  LABELS = {{ $labels }}"
        expr: (node_reboot_required > 0) * on(instance) group_left (nodename) node_uname_info{nodename=~".+"}
        for: 4h
        labels:
          alert_level: P4
          dispatch: devops
