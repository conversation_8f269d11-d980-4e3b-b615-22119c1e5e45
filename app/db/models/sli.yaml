user_facing:          
  - name: health_check
    group: health_check
    sli:
      - name: APIHealthCheckFailed        
        metric:  api_status_value{url="https://housesigma.com/bkv2/api/init/accesstoken/check"} != 1 or absent(api_status_value{url="https://housesigma.com/bkv2/api/init/accesstoken/check"})
        summary: 'API health check failed: {{ $labels.url }}'
        description: 'API health check failed: {{ $labels.url }}. MySQL, Redis or MongoDB might be experiencing issues.'
        for: 2m
        labels:
          alert_level: P0  
          dispatch: devops                       

supporting_software:                    
  - name: mysql
    group: mysql
    sli:
      - name: MysqlDown
        metric: mysql_up{instance!~"^hk.*"} == 0
        summary: 'MySQL down, instance {{ $labels.instance }}'
        description: 'MySQL instance is down on {{ $labels.instance }}'
        for: 3m
        labels:
          dispatch: devops
          alert_level: P0

      - name: HkMysqlDown
        metric: mysql_up{instance=~"^hk.*"} == 0
        summary: 'MySQL down, instance {{ $labels.instance }}'
        description: 'MySQL instance is down on {{ $labels.instance }}'
        for: 3m
        labels:
          dispatch: devops
          alert_level: P2          

      - name: MysqlTooManyConnections
        metric: max_over_time(mysql_global_status_threads_connected[1m]) / mysql_global_variables_max_connections * 100 > 80
        summary: 'MySQL too many connections (> 80%), instance {{ $labels.instance }}'
        description: 'MySQL too many connections (> 80%), instance {{ $labels.instance }}'
        for: 2m
        labels:
          dispatch: devops
          alert_level: P2

      - name: MysqlHighPreparedStatementsUtilization
        metric: max_over_time(mysql_global_status_prepared_stmt_count[1m]) / mysql_global_variables_max_prepared_stmt_count * 100 > 80
        summary: 'MySQL high prepared statements utilization (> 80%), instance {{ $labels.instance }}'
        description: 'MySQL high prepared statements utilization (> 80%), instance {{ $labels.instance }}'
        for: 2m
        labels:
          dispatch: devops
          alert_level: P2

      - name: MysqlSlaveIoThreadNotRunning
        metric: ( mysql_slave_status_slave_io_running and ON (instance) mysql_slave_status_master_server_id > 0 ) == 0
        summary: 'MySQL Slave IO thread not running, instance {{ $labels.instance }}'
        description: 'MySQL Slave IO thread not running, instance {{ $labels.instance }}'
        for: 10m
        labels:
          dispatch: devops
          alert_level: P0

      - name: MysqlSlaveSqlThreadNotRunning
        metric: ( mysql_slave_status_slave_sql_running and ON (instance) mysql_slave_status_master_server_id > 0) == 0
        summary: 'MySQL Slave SQL thread not running, instance {{ $labels.instance }}'
        description: 'MySQL Slave SQL thread not running, instance {{ $labels.instance }}'
        for: 0m
        labels:
          dispatch: devops
          alert_level: P0

      - name: MysqlSlaveReplicationLag
        metric: ( (mysql_slave_status_seconds_behind_master - mysql_slave_status_sql_delay) and ON (instance) mysql_slave_status_master_server_id > 0 ) > 60
        summary: 'MySQL Slave replication lag, instance {{ $labels.instance }}'
        description: 'MySQL Slave replication lag, instance {{ $labels.instance }}'
        for: 5m
        labels:
          dispatch: devops
          alert_level: P1

      - name: MysqlInnodbLogWaits
        metric: rate(mysql_global_status_innodb_log_waits[15m]) > 10
        summary: 'MySQL InnoDB log waits, instance {{ $labels.instance }}'
        description: 'MySQL InnoDB log waits, instance {{ $labels.instance }}'
        for: 5m
        labels:
          dispatch: devops
          alert_level: P2

      - name: MysqlRestarted
        metric: delta(mysql_global_status_uptime[15m]) < 0
        summary: 'MySQL restarted, instance {{ $labels.instance }}'
        description: 'MySQL restarted, instance {{ $labels.instance }}'
        for: 0m
        labels:
          dispatch: devops
          alert_level: P1           

  - name: redis
    group: redis
    sli:
      - name: RedisDown
        metric: redis_up == 0
        summary: 'Redis down, instance {{ $labels.instance }}'
        description: 'Redis down, instance {{ $labels.instance }}'
        for: 3m
        labels:
          dispatch: devops
          alert_level: P0

      - name: RedisMissingBackup
        metric: time() - redis_rdb_last_save_timestamp_seconds > 60 * 60 * 24
        summary: 'Redis missing backup, instance {{ $labels.instance }}'
        description: 'Redis missing backup, instance {{ $labels.instance }}'
        for: 0m
        labels:
          dispatch: devops
          alert_level: P1

      - name: RedisOutOfSystemMemory
        metric: redis_memory_used_bytes / redis_total_system_memory_bytes * 100 > 90
        summary: 'Redis out of system memory, instance {{ $labels.instance }}'
        description: 'Redis out of system memory, instance {{ $labels.instance }}'
        for: 5m
        labels:
          dispatch: devops
          alert_level: P1

      - name: RedisOutOfConfiguredMaxmemory
        metric: redis_memory_used_bytes / redis_memory_max_bytes * 100 > 90 and on(instance) redis_memory_max_bytes > 0
        summary: 'Redis out of configured maxmemory, instance {{ $labels.instance }}'
        description: 'Redis out of configured maxmemory, instance {{ $labels.instance }}'
        for: 5m
        labels:
          dispatch: devops
          alert_level: P1

      - name: RedisTooManyConnections
        metric: redis_connected_clients / redis_config_maxclients * 100 > 90
        summary: 'Redis too many connections, instance {{ $labels.instance }}'
        description: 'Redis too many connections, instance {{ $labels.instance }}'
        for: 5m
        labels:
          dispatch: devops
          alert_level: P1

      - name: RedisConnectionIsZero
        metric: redis_connected_clients == 0
        summary: 'No connection, instance {{ $labels.instance }}'
        description: 'No connection, instance {{ $labels.instance }}'
        for: 10m
        labels:
          dispatch: devops
          alert_level: P1

      - name: RedisRejectedConnections
        metric: increase(redis_rejected_connections_total[1m]) > 0
        summary: 'Redis rejected connections, instance {{ $labels.instance }}'
        description: 'Redis rejected connections, instance {{ $labels.instance }}'
        for: 1m
        labels:
          dispatch: devops
          alert_level: P0

  - name: mongo
    group: mongo
    sli:
      - name: MongodbDown
        metric: mongodb_up{instance!~'.*hk.*'} == 0
        summary: 'MongoDB Down, instance {{ $labels.instance }}'
        description: 'MongoDB Down, instance {{ $labels.instance }}'
        for: 5m
        labels:
          dispatch: devops
          alert_level: P0

      - name: MongodHKbDown
        metric: mongodb_up{instance=~'.*hk.*'} == 0
        summary: 'MongoDB Down, instance {{ $labels.instance }}'
        description: 'MongoDB Down, instance {{ $labels.instance }}'
        for: 5m
        labels:
          dispatch: devops
          alert_level: P2          

      - name: MongodbReplicaMemberUnhealthy
        metric: min(mongodb_rs_members_health{member_idx!~"^hk.*"}) by (member_idx) == 0
        summary: 'Mongodb replica member unhealthy, instance {{ $labels.member_idx }}'
        description: 'Mongodb replica member unhealthy, instance {{ $labels.member_idx }}'
        for: 5m
        labels:
          dispatch: devops
          alert_level: P0

      - name: MongodbHKReplicaMemberUnhealthy
        metric: min(mongodb_rs_members_health{member_idx=~"^hk.*"}) by (member_idx) == 0
        summary: 'Mongodb replica member unhealthy, instance {{ $labels.member_idx }}'
        description: 'Mongodb replica member unhealthy, instance {{ $labels.member_idx }}'
        for: 5m
        labels:
          dispatch: devops
          alert_level: P2          

      - name: MongodbReplicationLag
        metric: (mongodb_rs_members_optimeDate{member_state="PRIMARY"} - on (set) group_right mongodb_rs_members_optimeDate{member_state="SECONDARY"}) / 1000 > 10
        summary: 'MongoDB replication lag, instance {{ $labels.instance }}'
        description: 'MongoDB replication lag, instance {{ $labels.instance }}'
        for: 10m
        labels:
          dispatch: devops
          alert_level: P1

      - name: MongodbNumberCursorsOpen
        metric: mongodb_ss_metrics_cursor_open{csr_type="total"} > 10000
        summary: 'MongoDB number cursors open, instance {{ $labels.instance }}'
        description: 'MongoDB number cursors open, instance {{ $labels.instance }}'
        for: 10m
        labels:
          dispatch: devops
          alert_level: P1

      - name: MongodbCursorsTimeouts
        metric: increase(mongodb_ss_metrics_cursor_timedOut[1m]) > 100
        summary: 'MongoDB cursors timeouts, instance {{ $labels.instance }}'
        description: 'MongoDB cursors timeouts, instance {{ $labels.instance }}'
        for: 10m
        labels:
          dispatch: devops
          alert_level: P1

      - name: MongodbTooManyConnections
        metric: avg by(instance) (rate(mongodb_ss_connections{conn_type="current"}[1m])) / avg by(instance) (sum(mongodb_ss_connections) by (instance)) * 100 > 80
        summary: 'MongoDB too many connections, instance {{ $labels.instance }}'
        description: 'MongoDB too many connections, instance {{ $labels.instance }}'
        for: 5m
        labels:
          dispatch: devops
          alert_level: P1
           
      - name: ITSO_ConsentToAdvertise_ConsentToAdvertise_exist
        metric: mongo_matching_docs > 0
        summary: 'Either the field ITSO_ConsentToAdvertise or ConsentToAdvertise exists in MongoDB documents. <@915440516991176774>'
        description: 'Either ITSO_ConsentToAdvertise or rets.ConsentToAdvertise field exists in the MongoDB documents. <@915440516991176774>'
        for: 0m
        labels:
          alert_level: P2
          dispatch: devops                 

  - name: beanstalkd
    group: beanstalkd
    sli:
      - name: uptime_absent
        metric: absent(uptime{exported_instance="ovh110.fangintel.com:11300"})
        summary: 'Beanstalkd uptime metric missing for {{ $labels.instance }}'
        description: 'The uptime metric for instance {{ $labels.instance }} is missing. This may indicate that Beanstalkd exporter is not running or unreachable.'
        for: 1m
        labels:
          alert_level: P0
          dispatch: devops

      - name: jobs_ready_high
        metric: tube_current_jobs_ready{tube="v2_datafeed_llama_production"} > 4500
        summary: 'The tube {{ $labels.tube }} has {{ $value }} ready jobs, exceeding the threshold of 4500.'
        description: 'The tube {{ $labels.tube }} has {{ $value }} ready jobs, exceeding the threshold of 4500.'
        for: 1m
        labels:
          alert_level: P2
          dispatch: devops
          
  - name: abcproxy
    group: abcproxy
    sli:
      - name: Abcproxy Low Bandwidth Warning
        metric: bandwidth_balance_gb < 2
        summary: 'abcproxy Low Bandwidth Remaining'
        description: 'Only {{ $value }}GB of bandwidth left.'
        for: 0m
        labels:
          alert_level: P2
          dispatch: devops

      - name: Abcproxy Expiry Date Warning
        metric: expiry_days_remaining <= 2
        summary: 'abcproxy Service Expiry Warning'
        description: 'Service will expire in {{ $value }} days.'
        for: 0m
        labels:
          alert_level: P2
          dispatch: devops  

  - name: nginx
    group: nginx
    sli:
      - name: NginxServerDown
        metric: probe_success{env="prd"} == 0
        summary: 'Nginx server DOWN: url: {{ $labels.instance }} host: {{ $labels.cache_host }}.'
        description: 'Nginx server DOWN: url: {{ $labels.instance }} host: {{ $labels.cache_host }}.'
        for: 1m
        labels:
          alert_level: P1
          dispatch: devops
      - name: HighNginxLatencyP80
        metric: histogram_quantile(0.8, sum(rate(nginx_http_request_duration_seconds_bucket{status!="404|500|304|499"}[3m])) by (le, host)) > 2 
        summary: 'High Nginx latency P80: {{ $value }}s for host {{ $labels.host }}.'
        description: 'High Nginx latency P80: {{ $value }}s for host {{ $labels.host }}.'
        for: 0m
        labels:
          alert_level: P2
          dispatch: devops
      - name: NginxSuccess98Ratio
        metric: (sum by (host, server) (rate(nginx_http_requests_count{status=~"[23].."}[3m]))) / (sum by (host, server) (rate(nginx_http_requests_count[3m])) or vector(0)) < 0.98 
        summary: 'Nginx success rate for {{ $labels.host }} - {{ $labels.server }} is {{ printf "%.3f" $value }}.'
        description: 'Nginx success rate for {{ $labels.host }} - {{ $labels.server }} is {{ printf "%.3f" $value }}.'
        for: 0m
        labels:
          alert_level: P2
          dispatch: devops    
      - name: NginxSuccess95Ratio
        metric: (sum by (host, server) (rate(nginx_http_requests_count{status=~"[23].."}[3m]))) / (sum by (host, server) (rate(nginx_http_requests_count[3m])) or vector(0)) < 0.95 
        summary: 'Nginx success rate for {{ $labels.host }} - {{ $labels.server }} is {{ printf "%.3f" $value }}.'
        description: 'Nginx success rate for {{ $labels.host }} - {{ $labels.server }} is {{ printf "%.3f" $value }}.'
        for: 0m
        labels:
          alert_level: P2
          dispatch: devops
      - name: NginxSuccess90Ratio
        metric: (sum by (host, server) (rate(nginx_http_requests_count{status=~"[23].."}[3m]))) / (sum by (host, server) (rate(nginx_http_requests_count[3m])) or vector(0)) < 0.90
        summary: 'Nginx success rate for {{ $labels.host }} - {{ $labels.server }} is {{ printf "%.3f" $value }}.'
        description: 'Nginx success rate for {{ $labels.host }} - {{ $labels.server }} is {{ printf "%.3f" $value }}.'
        for: 0m
        labels:
          alert_level: P1
          dispatch: devops                     

                    

supporting_hardware:
  - name: server
    group: server
    sli:
      - name: HostIsDown
        metric: up{job="node", instance!~"^hk.*"} == 0
        summary: 'Host is down ({{ $labels.instance }}) for 3 mins'
        description: 'Host is down ({{ $labels.instance }}) for 3 mins'
        for: 3m
        labels:
          alert_level: P0
          dispatch: devops
      - name: HkHostIsDown
        metric: up{job="node", instance=~"^hk.*"} == 0
        summary: 'Host is down ({{ $labels.instance }}) for 10 mins'
        description: 'Host is down ({{ $labels.instance }}) for 10 mins'
        for: 10m
        labels:
          alert_level: P2
          dispatch: devops            
          
      
