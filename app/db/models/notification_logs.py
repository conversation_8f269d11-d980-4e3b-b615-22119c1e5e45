from sqlalchemy import Column, Integer, String, TIMESTAMP
from sqlalchemy.sql import func
from app.db.session import Base

class NotificationLog(Base):
    __tablename__ = "notification_logs"

    id = Column(Integer, primary_key=True, autoincrement=True, comment="自增主键")
    alert_id = Column(Integer, nullable=False, comment="报警表ID")
    notification_type = Column(String(50), nullable=False, comment="通知类型，如 sns, email, discord")
    notification_value = Column(String(255), nullable=False, comment="通知目标")
    status = Column(String(50), nullable=False, comment='报警状态，如 firing 或 resolved')
    created_at = Column(TIMESTAMP, server_default=func.now(), nullable=False, comment="记录创建时间")
    updated_at = Column(TIMESTAMP, server_default=func.now(), onupdate=func.now(), nullable=False, comment="记录最后更新时间")
    deleted_at = Column(TIMESTAMP, nullable=True, comment="软删除字段")
