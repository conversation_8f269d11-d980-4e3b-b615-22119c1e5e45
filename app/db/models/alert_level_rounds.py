from sqlalchemy import Column, Integer, String, TIMESTAMP, DateTime, func
from app.db.session import Base

class AlertLevelRounds(Base):
    __tablename__ = "alert_level_rounds"

    id = Column(Integer, primary_key=True, index=True)
    level_name = Column(String(255), nullable=False)  # Alert level name, e.g., P0, P1
    round = Column(Integer, nullable=False)  # Round number, e.g., 1, 2
    timeout_round = Column(Integer, nullable=True)  # Minimum duration (minutes) for this round
    timeout_error = Column(Integer, default=0)  # Allowed error margin in minutes
    frequency = Column(Integer, default=0)  # Frequency of notification in minutes
    notification_type = Column(String(50), default="sms")  # Type of notification for this round
    created_at = Column(DateTime(timezone=True), server_default=func.now())  # Record creation time
    updated_at = Column(DateTime(timezone=True), server_default=func.now(), onupdate=func.now())  # Last update time
    deleted_at = Column(TIMESTAMP, nullable=True)  # Soft delete field
