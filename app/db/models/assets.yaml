servers:
  - id: 1
    hostname: ovh01
    dns: ovh01.fangintel.com
    provider: ovh
    region: ca-east-bhs-a
    location: "North America (Canada – East – Beauharnois)"
    instance_type: "6c/12t 32GB"
    ip: "************"
    os: "centos8"
    status: active
    services:
      - name: nginx
        description: "Web服务器"
        startup_cmd: "systemctl start nginx"
        shutdown_cmd: "systemctl stop nginx"
        domains:
          - domain: "cache04.housesigma.com"
      - name: supervisord
        startup_cmd: "systemctl start supervisord"
        shutdown_cmd: "systemctl stop supervisord"
      - name: php
        description: "php"
        startup_cmd: "systemctl start php-fpm"
        shutdown_cmd: "systemctl stop php-fpm"
      - name: haproxy
        startup_cmd: "systemctl start haproxy"
        shutdown_cmd: "systemctl stop haproxy"
      - name: elasticsearch
        startup_cmd: "systemctl start elasticsearch"
        shutdown_cmd: "systemctl stop elasticsearch"
      - name: crontab
        startup_cmd: "systemctl start crond"
        shutdown_cmd: "systemctl stop crond"
      - name: ndoe_exporter
        startup_cmd: "systemctl start node_exporter"
        shutdown_cmd: "systemctl stop node_exporter"
      - name: vector
        startup_cmd: "systemctl start vector"
        shutdown_cmd: "systemctl stop vector"
  - id: 2
    hostname: ovh03
    dns: ovh03.fangintel.com
    provider: ovh
    region: ca-east-bhs-a
    location: "North America (Canada – East – Beauharnois)"
    instance_type: "6c/12t 64GB"
    ip: "***********"
    os: "centos8"
    status: active
    services:
      - name: nginx
        description: "Web服务器"
        startup_cmd: "systemctl start nginx"
        shutdown_cmd: "systemctl stop nginx"
        domains:
          - domain: "cache06.housesigma.com"
          - domain: "cache02.housesigma.com"
      - name: php
        description: "可忽略"
        startup_cmd: "systemctl start php-fpm"
        shutdown_cmd: "systemctl stop php-fpm"
      - name: mysql
        description: "mysql slave"
        startup_cmd: "systemctl start mysql"
        shutdown_cmd: "systemctl stop mysql"
      - name: ndoe_exporter
        startup_cmd: "systemctl start node_exporter"
        shutdown_cmd: "systemctl stop node_exporter"
      - name: vector
        startup_cmd: "systemctl start vector"
        shutdown_cmd: "systemctl stop vector"
  - id: 3
    hostname: ovh04
    dns: ovh04.fangintel.com
    provider: ovh
    region: ca-east-bhs-a
    location: "North America (Canada – East – Beauharnois)"
    instance_type: "8c/16t 64GB"
    ip: "***************"
    os: "centos8"
    status: active
    services:
  - id: 4
    hostname: ovh05
    dns: ovh05.fangintel.com
    provider: ovh
    region: ca-east-bhs-a
    location: "North America (Canada – East – Beauharnois)"
    instance_type: "6c/12t 64GB"
    ip: "************"
    os: "centos8"
    status: active
    services:
      - name: nginx
        description: "可忽略"
        startup_cmd: "systemctl start nginx"
        shutdown_cmd: "systemctl stop nginx"
      - name: php
        description: "可忽略"
        startup_cmd: "systemctl start php-fpm"
        shutdown_cmd: "systemctl stop php-fpm"
      - name: mysql
        description: "mysql master"
        startup_cmd: "systemctl start mysql"
        shutdown_cmd: "systemctl stop mysql"
      - name: mongo
        description: "可忽略"
        startup_cmd: "systemctl start mongod"
        shutdown_cmd: "systemctl stop mongod"
      - name: ndoe_exporter
        startup_cmd: "systemctl start node_exporter"
        shutdown_cmd: "systemctl stop node_exporter"
