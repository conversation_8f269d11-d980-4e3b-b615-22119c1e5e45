from sqlalchemy import Column, Integer, String, Text, TIMESTAMP
from sqlalchemy.sql import func
from app.db.session import Base

class Alert(Base):
    __tablename__ = "alerts"

    id = Column(Integer, primary_key=True, autoincrement=True, comment="自增主键")
    alertname = Column(String(255), nullable=False, comment="报警名称")
    status = Column(String(50), nullable=False, comment="报警状态，例如 firing, resolved")
    alert_level = Column(String(10), nullable=False, comment="报警级别 P0/P1/P2")
    start_at = Column(TIMESTAMP, nullable=False, comment="报警开始时间")
    instance = Column(String(255), nullable=False, comment="报警实例")
    content = Column(Text, nullable=False, comment="完整报警内容 JSON")
    created_at = Column(TIMESTAMP, server_default=func.now(), comment="创建时间")
    updated_at = Column(TIMESTAMP, server_default=func.now(), onupdate=func.now(), comment="更新时间")
