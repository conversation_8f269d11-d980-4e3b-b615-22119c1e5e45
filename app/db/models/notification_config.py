from sqlalchemy import Column, Integer, String, DateTime
from sqlalchemy.sql import func
from app.db.session import Base

class NotificationConfig(Base):
    __tablename__ = 'notification_config'

    id = Column(Integer, primary_key=True, autoincrement=True, comment='Auto-increment primary key')
    level_name = Column(String(20), nullable=True, comment='Alert level name, e.g., P0, P1, only for non-SMS notifications')
    alert_level_round_id = Column(Integer, nullable=True, comment='ID from alert_level_rounds table, only used for SMS notifications')
    notification_type = Column(String(50), nullable=False, comment='Notification type, e.g., sms, email, discord')
    notification_value = Column(String(255), nullable=False, comment='Notification target, e.g., phone number, email, webhook URL')
    created_at = Column(DateTime, server_default=func.now(), comment='Creation time')
    updated_at = Column(DateTime, server_default=func.now(), onupdate=func.now(), comment='Last update time')
    deleted_at = Column(DateTime, nullable=True, comment='Soft delete field')
