servers:
  - id: 1
    hostname: ovh01
    dns: ovh01.fangintel.com
    provider: ovh
    region: ca-east-bhs-a
    location: "North America (Canada – East – Beauharnois)"
    instance_type: "6c/12t 32GB"
    ip: "************"
    os: "centos8"
    status: active
    services:
      - name: nginx
        description: "Web服务器"
        startup_cmd: "systemctl start nginx"
        shutdown_cmd: "systemctl stop nginx"
        domains:
          - domain: "cache04.housesigma.com"
      - name: supervisord
        startup_cmd: "systemctl start supervisord"
        shutdown_cmd: "systemctl stop supervisord"
      - name: php
        description: "php"
        startup_cmd: "systemctl start php-fpm"
        shutdown_cmd: "systemctl stop php-fpm"
      - name: haproxy
        startup_cmd: "systemctl start haproxy"
        shutdown_cmd: "systemctl stop haproxy"
      - name: elasticsearch
        startup_cmd: "systemctl start elasticsearch"
        shutdown_cmd: "systemctl stop elasticsearch"
      - name: crontab
        startup_cmd: "systemctl start crond"
        shutdown_cmd: "systemctl stop crond"
      - name: node_exporter
        startup_cmd: "systemctl start node_exporter"
        shutdown_cmd: "systemctl stop node_exporter"
      - name: vector
        startup_cmd: "systemctl start vector"
        shutdown_cmd: "systemctl stop vector"
  - id: 2
    hostname: ovh03
    dns: ovh03.fangintel.com
    provider: ovh
    region: ca-east-bhs-a
    location: "North America (Canada – East – Beauharnois)"
    instance_type: "6c/12t 64GB"
    ip: "***********"
    os: "centos8"
    status: active
    services:
      - name: nginx
        description: "Web服务器"
        startup_cmd: "systemctl start nginx"
        shutdown_cmd: "systemctl stop nginx"
        domains:
          - domain: "cache06.housesigma.com"
          - domain: "cache02.housesigma.com"
      - name: php
        description: "可忽略"
        startup_cmd: "systemctl start php-fpm"
        shutdown_cmd: "systemctl stop php-fpm"
      - name: mysql
        description: "mysql slave"
        startup_cmd: "systemctl start mysql"
        shutdown_cmd: "systemctl stop mysql"
      - name: node_exporter
        startup_cmd: "systemctl start node_exporter"
        shutdown_cmd: "systemctl stop node_exporter"
      - name: vector
        startup_cmd: "systemctl start vector"
        shutdown_cmd: "systemctl stop vector"
  - id: 3
    hostname: ovh04
    dns: ovh04.fangintel.com
    provider: ovh
    region: ca-east-bhs-a
    location: "North America (Canada – East – Beauharnois)"
    instance_type: "8c/16t 64GB"
    ip: "***************"
    os: "centos8"
    status: active
    services:
  - id: 4
    hostname: ovh05
    dns: ovh05.fangintel.com
    provider: ovh
    region: ca-east-bhs-a
    location: "North America (Canada – East – Beauharnois)"
    instance_type: "6c/12t 64GB"
    ip: "************"
    os: "centos8"
    status: active
    services:
      - name: nginx
        description: "可忽略"
        startup_cmd: "systemctl start nginx"
        shutdown_cmd: "systemctl stop nginx"
      - name: php
        description: "可忽略"
        startup_cmd: "systemctl start php-fpm"
        shutdown_cmd: "systemctl stop php-fpm"
      - name: mysql
        description: "mysql master"
        startup_cmd: "systemctl start mysql"
        shutdown_cmd: "systemctl stop mysql"
      - name: mongo
        description: "可忽略"
        startup_cmd: "systemctl start mongod"
        shutdown_cmd: "systemctl stop mongod"
      - name: node_exporter
        startup_cmd: "systemctl start node_exporter"
        shutdown_cmd: "systemctl stop node_exporter"
  - id: 5
    hostname: ovh06
    dns: ovh06.fangintel.com
    provider: ovh
    region: ca-east-bhs-a
    location: "North America (Canada – East – Beauharnois)"
    instance_type: "16c/32t 128GB"
    ip: "**************"
    os: "Ubuntu 18"
    status: active
    services:
      - name: nginx
        startup_cmd: "systemctl start nginx"
        shutdown_cmd: "systemctl stop nginx"
        domains:
          - domain: "ovh06.fangintel.com"
      - name: pm2
        cmd: /root/.nvm/versions/node/v16.20.2/lib/node_modules/pm2/bin/pm2
        apps:
          - app: "rendertron-desktop"
          - app: "rendertron-mobile" 
      - name: docker
        startup_cmd: "systemctl start docker"
        shutdown_cmd: "systemctl stop docker"
        containers:
          - container: "locustio/locust"
          - container: "portainer/agent"
          - container: "openmaptiles/postgis"
      - name: node_exporter
        startup_cmd: "systemctl start node_exporter"
        shutdown_cmd: "systemctl stop node_exporter"
  - id: 6
    hostname: ovh07
    dns: ovh07.fangintel.com
    provider: ovh
    region: ca-east-bhs-a
    location: "North America (Canada – East – Beauharnois)"
    instance_type: "4c/8t 32GB"
    ip: "************"
    os: "centos8"
    status: active
    description: "stg"
    services:
      - name: nginx
        startup_cmd: "systemctl start nginx"
        shutdown_cmd: "systemctl stop nginx"
        domains:
          - domain: "dev.housesigma.com"
      - name: pm2
        cmd: pm2
        apps:
          - app: "rendertron-desktop"
          - app: "rendertron-mobile" 
      - name: supervisord
        startup_cmd: "systemctl start supervisord"
        shutdown_cmd: "systemctl stop supervisord"
      - name: php
        description: "php"
        startup_cmd: "systemctl start php-fpm"
        shutdown_cmd: "systemctl stop php-fpm"
      - name: mysql
        startup_cmd: "systemctl start mysql"
        shutdown_cmd: "systemctl stop mysql"
      - name: mongo
        startup_cmd: "systemctl start mongod"
        shutdown_cmd: "systemctl stop mongod"      
      - name: node_exporter
        startup_cmd: "systemctl start node_exporter"
        shutdown_cmd: "systemctl stop node_exporter" 
 - id: 7
    hostname: ovh08
    dns: ovh08.fangintel.com
    provider: ovh
    region: ca-east-bhs-a
    location: "North America (Canada – East – Beauharnois)"
    instance_type: "6c/12t 32GB"
    ip: "**************"
    os: "centos8"
    status: active
    services:
      - name: nginx
        startup_cmd: "systemctl start nginx"
        shutdown_cmd: "systemctl stop nginx"
        description: "rendertron proxy"
        domains:
          - domain: "housesigma.com"
            description: "api"
          - domain: "static.housesigma.com"
      - name: php
        description: "php"
        startup_cmd: "systemctl start php-fpm"
        shutdown_cmd: "systemctl stop php-fpm" 
      - name: mongo
        description: "可忽略"
        startup_cmd: "systemctl start mongod"
        shutdown_cmd: "systemctl stop mongod"  
      - name: haproxy rendertron
        startup_cmd: "systemctl start haproxy"
        shutdown_cmd: "systemctl stop haproxy"                       
      - name: pm2
        cmd: pm2
        apps:
          - app: "rendertron-desktop"
          - app: "rendertron-mobile"  
      - name: node_exporter
        startup_cmd: "systemctl start node_exporter"
        shutdown_cmd: "systemctl stop node_exporter" 
  - id: 8
    hostname: ovh09
    dns: ovh09.fangintel.com
    provider: ovh
    region: ca-east-bhs-a
    location: "North America (Canada – East – Beauharnois)"
    instance_type: "6c/12t 32GB"
    ip: "**************"
    os: "centos8"
    status: active
    services:
      - name: nginx
        startup_cmd: "systemctl start nginx"
        shutdown_cmd: "systemctl stop nginx"
        domains:
          - domain: "rendertron.fangintel.com"
          - domain: "cache09.housesigma.com"
      - name: php
        description: "php"
        startup_cmd: "systemctl start php-fpm"
        shutdown_cmd: "systemctl stop php-fpm"                       
      - name: pm2
        cmd: pm2
        apps:
          - app: "rendertron-desktop"
          - app: "rendertron-mobile"  
      - name: node_exporter
        startup_cmd: "systemctl start node_exporter"
        shutdown_cmd: "systemctl stop node_exporter" 
      - name: vector
        startup_cmd: "systemctl start vector"
        shutdown_cmd: "systemctl stop vector"  
  - id: 9
    hostname: ovh10
    dns: ovh10.fangintel.com
    provider: ovh
    region: ca-east-bhs-a
    location: "North America (Canada – East – Beauharnois)"
    instance_type: "4c/8t 32GB"
    ip: "*************"
    os: "Rocky Linux 8"
    status: active
    services:                   
  - id: 10
    hostname: ovh11
    dns: ovh11.fangintel.com
    provider: ovh
    region: ca-east-bhs-a
    location: "North America (Canada – East – Beauharnois)"
    instance_type: "6c/12t 64GB"
    ip: "************"
    os: "centos8"
    status: active
    services:
      - name: nginx
        startup_cmd: "systemctl start nginx"
        shutdown_cmd: "systemctl stop nginx"
        domains:
      - name: php
        description: "php"
        startup_cmd: "systemctl start php-fpm"
        shutdown_cmd: "systemctl stop php-fpm"                       
      - name: mongo
        description: "mongo-session prd和dev放在一起"
        startup_cmd: "systemctl start mongod"
        shutdown_cmd: "systemctl stop mongod"  
      - name: redis
        startup_cmd: "systemctl start redis"
        shutdown_cmd: "systemctl stop redis"  
      - name: haproxy 主要
        startup_cmd: "systemctl start haproxy"
        shutdown_cmd: "systemctl stop haproxy"                
      - name: node_exporter
        startup_cmd: "systemctl start node_exporter"
        shutdown_cmd: "systemctl stop node_exporter" 
  - id: 11
    hostname: ovh12
    dns: ovh12.fangintel.com
    provider: ovh
    region: ca-east-bhs-a
    location: "North America (Canada – East – Beauharnois)"
    instance_type: "6c/12t 64GB"
    ip: "*************"
    os: "centos8"
    status: active
    services:
      - name: nginx
        startup_cmd: "systemctl start nginx"
        shutdown_cmd: "systemctl stop nginx"
        domains:
      - name: php
        description: "php"
        startup_cmd: "systemctl start php-fpm"
        shutdown_cmd: "systemctl stop php-fpm"                       
      - name: elasticsearch
        startup_cmd: "systemctl start elasticsearch"
        shutdown_cmd: "systemctl stop elasticsearch"  
      - name: beanstalkd
        startup_cmd: "systemctl start beanstalkd"
        shutdown_cmd: "systemctl stop beanstalkd"                
      - name: node_exporter
        startup_cmd: "systemctl start node_exporter"
        shutdown_cmd: "systemctl stop node_exporter" 
  - id: 12
    hostname: ovh13
    dns: ovh13.fangintel.com
    provider: ovh
    region: ca-east-bhs-a
    location: "North America (Canada – East – Beauharnois)"
    instance_type: "6c/12t 64GB"
    ip: "*************"
    os: "centos8"
    status: active
    services:
      - name: nginx
        startup_cmd: "systemctl start nginx"
        shutdown_cmd: "systemctl stop nginx"
        domains:
          - domain: "map.housesigma.com" 
          - domain: "mapeast.fangintel.com" 
          - domain: "map-ovh13.fangintel.com"
      - name: php
        description: "php"
        startup_cmd: "systemctl start php-fpm"
        shutdown_cmd: "systemctl stop php-fpm"                       
      - name: mysql
        description: "mysql slave"
        startup_cmd: "systemctl start mysql"
        shutdown_cmd: "systemctl stop mysql"                 
      - name: node_exporter
        startup_cmd: "systemctl start node_exporter"
        shutdown_cmd: "systemctl stop node_exporter" 
  - id: 13
    hostname: ovh14
    dns: ovh14.fangintel.com
    provider: ovh
    region: ca-east-bhs-a
    location: "North America (Canada – East – Beauharnois)"
    instance_type: "6c/12t 64GB"
    ip: "*************"
    os: "centos8"
    status: active
    services:
      - name: nginx
        startup_cmd: "systemctl start nginx"
        shutdown_cmd: "systemctl stop nginx"
        domains:
          - domain: "cache14.housesigma.com"
      - name: php
        description: "php"
        startup_cmd: "systemctl start php-fpm"
        shutdown_cmd: "systemctl stop php-fpm"                       
      - name: elasticsearch
        startup_cmd: "systemctl start elasticsearch"
        shutdown_cmd: "systemctl stop elasticsearch"                 
      - name: node_exporter
        startup_cmd: "systemctl start node_exporter"
        shutdown_cmd: "systemctl stop node_exporter" 
      - name: vector
        startup_cmd: "systemctl start vector"
        shutdown_cmd: "systemctl stop vector" 
  - id: 14
    hostname: ovh15
    dns: ovh15.fangintel.com
    provider: ovh
    region: ca-east-bhs-a
    location: "North America (Canada – East – Beauharnois)"
    instance_type: "6c/12t 64GB"
    ip: "*************"
    os: "centos8"
    status: active
    services:
      - name: nginx
        startup_cmd: "systemctl start nginx"
        shutdown_cmd: "systemctl stop nginx"
        domains:
          - domain: "cache15.housesigma.com"
      - name: php
        description: "php"
        startup_cmd: "systemctl start php-fpm"
        shutdown_cmd: "systemctl stop php-fpm" 
      - name: mysql
        description: "mysql slave"
        startup_cmd: "systemctl start mysql"
        shutdown_cmd: "systemctl stop mysql"                      
      - name: elasticsearch
        startup_cmd: "systemctl start elasticsearch"
        shutdown_cmd: "systemctl stop elasticsearch"                 
      - name: node_exporter
        startup_cmd: "systemctl start node_exporter"
        shutdown_cmd: "systemctl stop node_exporter" 
      - name: vector
        startup_cmd: "systemctl start vector"
        shutdown_cmd: "systemctl stop vector"
  - id: 15
    hostname: ovh16
    dns: ovh16.fangintel.com
    provider: ovh
    region: ca-east-bhs-a
    location: "North America (Canada – East – Beauharnois)"
    instance_type: "6c/12t 64GB"
    ip: "************"
    os: "centos8"
    status: active
    services:
      - name: nginx
        startup_cmd: "systemctl start nginx"
        shutdown_cmd: "systemctl stop nginx"
        domains:
          - domain: "cache16.housesigma.com"
          - domain: "map.housesigma.com" 
          - domain: "mapeast.fangintel.com"
      - name: php
        description: "php"
        startup_cmd: "systemctl start php-fpm"
        shutdown_cmd: "systemctl stop php-fpm"                       
      - name: elasticsearch
        startup_cmd: "systemctl start elasticsearch"
        shutdown_cmd: "systemctl stop elasticsearch"                 
      - name: node_exporter
        startup_cmd: "systemctl start node_exporter"
        shutdown_cmd: "systemctl stop node_exporter" 
      - name: vector
        startup_cmd: "systemctl start vector"
        shutdown_cmd: "systemctl stop vector"                                
  - id: 16
    hostname: ovh17
    dns: ovh17.fangintel.com
    provider: ovh
    region: ca-east-bhs-a
    location: "North America (Canada – East – Beauharnois)"
    instance_type: "6c/12t 64GB"
    ip: "************"
    os: "centos8"
    status: active
    services:
      - name: nginx
        startup_cmd: "systemctl start nginx"
        shutdown_cmd: "systemctl stop nginx"
        domains:
          - domain: "cache17.housesigma.com"
      - name: mongo
        description: "mongo replica"
        startup_cmd: "systemctl start mongod"
        shutdown_cmd: "systemctl stop mongod"                          
      - name: elasticsearch
        startup_cmd: "systemctl start elasticsearch"
        shutdown_cmd: "systemctl stop elasticsearch"                 
      - name: node_exporter
        startup_cmd: "systemctl start node_exporter"
        shutdown_cmd: "systemctl stop node_exporter" 
      - name: vector
        startup_cmd: "systemctl start vector"
        shutdown_cmd: "systemctl stop vector"  
  - id: 17
    hostname: ovh18
    dns: ovh18.fangintel.com
    provider: ovh
    region: ca-east-bhs-a
    location: "North America (Canada – East – Beauharnois)"
    instance_type: "6c/12t 64GB"
    ip: "*************"
    os: "centos8"
    status: active
    services:
      - name: nginx
        startup_cmd: "systemctl start nginx"
        shutdown_cmd: "systemctl stop nginx"
        domains:
          - domain: "cache18.housesigma.com"                          
      - name: elasticsearch
        startup_cmd: "systemctl start elasticsearch"
        shutdown_cmd: "systemctl stop elasticsearch"                 
      - name: node_exporter
        startup_cmd: "systemctl start node_exporter"
        shutdown_cmd: "systemctl stop node_exporter" 
      - name: vector
        startup_cmd: "systemctl start vector"
        shutdown_cmd: "systemctl stop vector"      
  - id: 18
    hostname: ovh19
    dns: ovh19.fangintel.com
    provider: ovh
    region: ca-east-bhs-a
    location: "North America (Canada – East – Beauharnois)"
    instance_type: "6c/12t 64GB"
    ip: "*************"
    os: "centos8"
    status: active
    services:
      - name: nginx
        startup_cmd: "systemctl start nginx"
        shutdown_cmd: "systemctl stop nginx"
        domains:
          - domain: "cache19.housesigma.com" 
          - domain: "satellite.housesigma.com"                                         
      - name: node_exporter
        startup_cmd: "systemctl start node_exporter"
        shutdown_cmd: "systemctl stop node_exporter" 
      - name: vector
        startup_cmd: "systemctl start vector"
        shutdown_cmd: "systemctl stop vector" 
  - id: 19
    hostname: ovh20
    dns: ovh20.fangintel.com
    provider: ovh
    region: ca-east-bhs-a
    location: "North America (Canada – East – Beauharnois)"
    instance_type: "6c/12t 64GB"
    ip: "*************"
    os: "centos8"
    status: active
    services:
      - name: nginx
        startup_cmd: "systemctl start nginx"
        shutdown_cmd: "systemctl stop nginx"
        domains:
      - name: mongo
        description: "mongo master"
        startup_cmd: "systemctl start mongod"
        shutdown_cmd: "systemctl stop mongod"                             
      - name: elasticsearch
        startup_cmd: "systemctl start elasticsearch"
        shutdown_cmd: "systemctl stop elasticsearch"                 
      - name: node_exporter
        startup_cmd: "systemctl start node_exporter"
        shutdown_cmd: "systemctl stop node_exporter" 
  - id: 20
    hostname: ovh21
    dns: ovh21.fangintel.com
    provider: ovh
    region: ca-east-bhs-a
    location: "North America (Canada – East – Beauharnois)"
    instance_type: "6c/12t 64GB"
    ip: "*************"
    os: "Rocky Linux 8"
    status: active
    services:
      - name: nginx
        startup_cmd: "systemctl start nginx"
        shutdown_cmd: "systemctl stop nginx"
        domains:
          - domain: "mapraster.housesigma.com"
          - domain: "ovh21.fangintel.com"
      - name: docker
        startup_cmd: "systemctl start docker"
        shutdown_cmd: "systemctl stop docker"
        containers:
          - container: "maptiler/tileserver-gl"               
      - name: node_exporter
        startup_cmd: "systemctl start node_exporter"
        shutdown_cmd: "systemctl stop node_exporter" 
  - id: 21
    hostname: ovh22
    dns: ovh22.fangintel.com
    provider: ovh
    region: ca-east-bhs-a
    location: "North America (Canada – East – Beauharnois)"
    instance_type: "6c/12t 32GB"
    ip: "*************"
    os: "Rocky Linux 8"
    status: active
    services:
      - name: nginx
        startup_cmd: "systemctl start nginx"
        shutdown_cmd: "systemctl stop nginx"
        domains:
      - name: pm2
        cmd: pm2
        apps:
          - app: "rendertron-desktop"
          - app: "rendertron-mobile"             
      - name: node_exporter
        startup_cmd: "systemctl start node_exporter"
        shutdown_cmd: "systemctl stop node_exporter" 
  - id: 22
    hostname: ovh23
    dns: ovh23.fangintel.com
    provider: ovh
    region: ca-east-bhs-a
    location: "North America (Canada – East – Beauharnois)"
    instance_type: "6c/12t 64GB"
    ip: "*************"
    os: "Rocky Linux 8"
    status: active
    services:
      - name: nginx
        startup_cmd: "systemctl start nginx"
        shutdown_cmd: "systemctl stop nginx"
        domains:
      - name: pm2
        cmd: pm2
        apps:
          - app: "rendertron-desktop"
          - app: "rendertron-mobile"             
      - name: node_exporter
        startup_cmd: "systemctl start node_exporter"
        shutdown_cmd: "systemctl stop node_exporter" 
  - id: 23
    hostname: ovh24
    dns: ovh24.fangintel.com
    provider: ovh
    region: ca-east-bhs-a
    location: "North America (Canada – East – Beauharnois)"
    instance_type: "6c/12t 64GB"
    ip: "************"
    os: "Rocky Linux 8"
    status: active
    services:
      - name: nginx
        startup_cmd: "systemctl start nginx"
        shutdown_cmd: "systemctl stop nginx"
        domains:
      - name: mysql
        description: "mysql replica"
        startup_cmd: "systemctl start mysql"
        shutdown_cmd: "systemctl stop mysql"
      - name: mongo
        description: "mongo replica"
        startup_cmd: "systemctl start mongod"
        shutdown_cmd: "systemctl stop mongod"             
      - name: node_exporter
        startup_cmd: "systemctl start node_exporter"
        shutdown_cmd: "systemctl stop node_exporter"  
  - id: 24
    hostname: ovh25
    dns: ovh25.fangintel.com
    provider: ovh
    region: ca-east-bhs-a
    location: "North America (Canada – East – Beauharnois)"
    instance_type: "6c/12t 64GB"
    ip: "*************"
    os: "Rocky Linux 8"
    status: active
    services:
      - name: nginx
        startup_cmd: "systemctl start nginx"
        shutdown_cmd: "systemctl stop nginx"
        domains:
      - name: php
        description: "php"
        startup_cmd: "systemctl start php-fpm"
        shutdown_cmd: "systemctl stop php-fpm"   
      - name: mysql
        description: "mysql replica"
        startup_cmd: "systemctl start mysql"
        shutdown_cmd: "systemctl stop mysql"             
      - name: node_exporter
        startup_cmd: "systemctl start node_exporter"
        shutdown_cmd: "systemctl stop node_exporter" 
  - id: 25
    hostname: ovh26
    dns: ovh26.fangintel.com
    provider: ovh
    region: ca-east-bhs-a
    location: "North America (Canada – East – Beauharnois)"
    instance_type: "8c/16t 64GB"
    ip: "*************"
    os: "Rocky Linux 8"
    status: active
    services:
      - name: nginx
        startup_cmd: "systemctl start nginx"
        shutdown_cmd: "systemctl stop nginx"
        domains:
          - domain: "localhost"
            description: "rendertron"
      - name: php
        description: "php"
        startup_cmd: "systemctl start php-fpm"
        shutdown_cmd: "systemctl stop php-fpm"   
      - name: pm2
        cmd: pm2
        apps:
          - app: "rendertron-desktop"
          - app: "rendertron-mobile" 
      - name: supervisord
        startup_cmd: "systemctl start supervisord"
        shutdown_cmd: "systemctl stop supervisord"    
      - name: node_exporter
        startup_cmd: "systemctl start node_exporter"
        shutdown_cmd: "systemctl stop node_exporter"  
      - name: vector
        startup_cmd: "systemctl start vector"
        shutdown_cmd: "systemctl stop vector"  
  - id: 26
    hostname: ovh27
    dns: ovh27.fangintel.com
    provider: ovh
    region: ca-east-bhs-a
    location: "North America (Canada – East – Beauharnois)"
    instance_type: "8c/16t 64GB"
    ip: "*************"
    os: "Rocky Linux 8"
    status: active
    services:
      - name: nginx
        startup_cmd: "systemctl start nginx"
        shutdown_cmd: "systemctl stop nginx"
        domains: 
      - name: node_exporter
        startup_cmd: "systemctl start node_exporter"
        shutdown_cmd: "systemctl stop node_exporter"  
      - name: vector
        startup_cmd: "systemctl start vector"
        shutdown_cmd: "systemctl stop vector"  
  - id: 27
    hostname: ovh28
    dns: ovh28.fangintel.com
    provider: ovh
    region: ca-east-bhs-a
    location: "North America (Canada – East – Beauharnois)"
    instance_type: "8c/16t 64GB"
    ip: "**************"
    os: "Rocky Linux 8"
    status: active
    services:
      - name: nginx
        startup_cmd: "systemctl start nginx"
        shutdown_cmd: "systemctl stop nginx"
        domains:
          - domain: "api.housesigma.com"  
      - name: node_exporter
        startup_cmd: "systemctl start node_exporter"
        shutdown_cmd: "systemctl stop node_exporter"  
      - name: vector
        startup_cmd: "systemctl start vector"
        shutdown_cmd: "systemctl stop vector"
  - id: 28
    hostname: ovh29
    dns: ovh29.fangintel.com
    provider: ovh
    region: ca-east-bhs-a
    location: "North America (Canada – East – Beauharnois)"
    instance_type: "6c/12t 64GB"
    ip: "************"
    os: "Rocky Linux 8"
    status: active
    services:
      - name: nginx
        startup_cmd: "systemctl start nginx"
        shutdown_cmd: "systemctl stop nginx"
        domains:
      - name: php
        description: "php"
        startup_cmd: "systemctl start php-fpm"
        shutdown_cmd: "systemctl stop php-fpm"                       
      - name: mysql
        description: "mysql replica"
        startup_cmd: "systemctl start mysql"
        shutdown_cmd: "systemctl stop mysql"                  
      - name: node_exporter
        startup_cmd: "systemctl start node_exporter"
        shutdown_cmd: "systemctl stop node_exporter"  
  - id: 29
    hostname: ovh30
    dns: ovh30.fangintel.com
    provider: ovh
    region: ca-east-bhs-a
    location: "North America (Canada – East – Beauharnois)"
    instance_type: "6c/12t 128GB"
    ip: "***************"
    os: "Rocky Linux 8"
    status: active
    services:
      - name: docker
        startup_cmd: "systemctl start docker"
        shutdown_cmd: "systemctl stop docker"
        containers:
          - container: "train-sqft-pred"
          - container: "portainer_edge_agent_2"
          - container: "train-community-popularity-index" 
          - container: "train-municipality-popularity-index"
          - container: "train-offmarket-estimate-identifier"   
          - container: "train-active-comparable"
          - container: "train-sold-comparable"
          - container: "train-active-estimate-identifier"
          - container: "mongodb"
          - container: "nginx-proxy-manager"
          - container: "ovh30-redis"
          - container: "mc-client"
  - id: 30
    hostname: ovh31
    dns: ovh31.fangintel.com
    provider: ovh
    region: ca-east-bhs-a
    location: "North America (Canada – East – Beauharnois)"
    instance_type: "6c/12t 128GB"
    ip: "************"
    os: "Rocky Linux 8"
    status: active
    services:
      - name: docker
        startup_cmd: "systemctl start docker"
        shutdown_cmd: "systemctl stop docker"
        containers:
          - container: "read_user_action"
          - container: "community-recommender"
          - container: "popularity-index-1" 
          - container: "popularity-index-2"
          - container: "portainer_edge_agent"   
          - container: "municipality-popularity-index"
          - container: "community-popularity-index"
          - container: "python-docker-webservice-1"
          - container: "python-docker-recommend-1"
          - container: "mongodb"
          - container: "recsys-cangen-ranker"
          - container: "nginx-proxy-manager"
          - container: "redis"
          - container: "mysql"
          - container: "mc-client"
  - id: 31
    hostname: ovh32
    dns: ovh32.fangintel.com
    provider: ovh
    region: ca-east-bhs-a
    location: "North America (Canada – East – Beauharnois)"
    instance_type: "16c/32t 128GB"
    ip: "*************"
    os: "Rocky Linux 8"
    status: active
    services:
      - name: docker
        startup_cmd: "systemctl start docker"
        shutdown_cmd: "systemctl stop docker"
        containers:
          - container: "locustio"
          - container: "pythoncore-webservice-1"
          - container: "popularity-index-1" 
          - container: "popularity-index-2"
          - container: "portainer_edge_agent"   
          - container: "municipality-popularity-index"
          - container: "community-popularity-index"
          - container: "active-avm-v5"
          - container: "active-avm"
          - container: "tax-pred"
          - container: "pythoncore-recommend-1"
          - container: "nginx-proxy-manager"
          - container: "mc-client"
  - id: 32
    hostname: ovh33
    dns: ovh33.fangintel.com
    provider: ovh
    region: ca-east-bhs-a
    location: "North America (Canada – East – Beauharnois)"
    instance_type: "6c/12t 128GB"
    ip: "************"
    os: "Rocky Linux 8"
    status: active
    services:
      - name: docker
        startup_cmd: "systemctl start docker"
        shutdown_cmd: "systemctl stop docker"
        containers:
          - container: "job-price-2-2"
          - container: "job-price-1-2"
          - container: "job-price-2-1"
          - container: "job-price-1-1"
          - container: "stg-recsys-cangen-ranker"
          - container: "stg-community-popularity-trend"
          - container: "api-job-active-dom"
          - container: "api-job-offmarket-avm"
          - container: "job-active-avm"
          - container: "stg-community_recommender"
          - container: "api-job-active-estimate-identifier"
          - container: "job-comparable-2-2"
          - container: "job-comparable-1-2"
          - container: "job-comparable-2-1"
          - container: "job-comparable-1-1"
          - container: "api-job-webservice-1"
          - container: "api-job-webservice-2"
          - container: "portainer_edge_agent"
          - container: "stg-municipality-popularity-trend"
          - container: "stg-sold-listing-comparable"
          - container: "stg-active-listing-comparable"
          - container: "api-job-taxes-pred"
          - container: "stg-pythonservices-webservice"
          - container: "stg-pythonservices-recommend"
          - container: "stg-offmarket-estimate-identifier"
          - container: "stg-popularity-index"
          - container: "api-job-sqft-pred"
          - container: "api-job-offmarket-estimate-identifier"
          - container: "nginx-proxy-manager"
          - container: "minio-mc"                      
  - id: 33
    hostname: ovh34
    dns: ovh34.fangintel.com
    provider: ovh
    region: ca-east-bhs-a
    location: "North America (Canada – East – Beauharnois)"
    instance_type: "12c/24t 32GB"
    ip: "************"
    os: "Rocky Linux 8"
    status: active
    services:
      - name: docker
        startup_cmd: "systemctl start docker"
        shutdown_cmd: "systemctl stop docker"
        containers:
          - container: "active-comparable-1"
          - container: "active-comparable-2"
          - container: "portainer_edge_agent"   
          - container: "nginx-proxy-manager"
  - id: 34
    hostname: ovh35
    dns: ovh35.fangintel.com
    provider: ovh
    region: ca-east-bhs-a
    location: "North America (Canada – East – Beauharnois)"
    instance_type: "12c/24t 96GB"
    ip: "*************"
    os: "Rocky Linux 8"
    status: active
    services:
      - name: docker
        startup_cmd: "systemctl start docker"
        shutdown_cmd: "systemctl stop docker"
        containers:
          - container: "recsys-cangen-ranker"
          - container: "portainer_edge_agent"   
          - container: "nginx-proxy-manager" 
  - id: 35
    hostname: ovh36
    dns: ovh36.fangintel.com
    provider: ovh
    region: ca-east-bhs-a
    location: "North America (Canada – East – Beauharnois)"
    instance_type: "12c/24t 96GB"
    ip: "*************"
    os: "Rocky Linux 8"
    status: active
    services:
      - name: docker
        startup_cmd: "systemctl start docker"
        shutdown_cmd: "systemctl stop docker"
        containers:
          - container: "recsys-cangen-ranker"
          - container: "portainer_edge_agent"   
          - container: "nginx-proxy-manager" 
  - id: 36
    hostname: ovh37
    dns: ovh37.fangintel.com
    provider: ovh
    region: ca-east-bhs-a
    location: "North America (Canada – East – Beauharnois)"
    instance_type: "6c/12t 64GB"
    ip: "*************"
    os: "Rocky Linux 8"
    status: active
    services:
      - name: docker
        startup_cmd: "systemctl start docker"
        shutdown_cmd: "systemctl stop docker"
        containers:
          - container: "sold-comparable"
          - container: "portainer_edge_agent"   
          - container: "nginx-proxy-manager"  
  - id: 37
    hostname: ovh38
    dns: ovh38.fangintel.com
    provider: ovh
    region: ca-east-bhs-a
    location: "North America (Canada – East – Beauharnois)"
    instance_type: "6c/12t 64GB"
    ip: "*************"
    os: "Rocky Linux 8"
    status: active
    services:
      - name: docker
        startup_cmd: "systemctl start docker"
        shutdown_cmd: "systemctl stop docker"
        containers:
          - container: "sold-comparable"
          - container: "portainer_edge_agent"   
          - container: "nginx-proxy-manager"  
  - id: 38
    hostname: ovh39
    dns: ovh39.fangintel.com
    provider: ovh
    region: ca-east-bhs-a
    location: "North America (Canada – East – Beauharnois)"
    instance_type: "6c/12t 64GB"
    ip: "*************"
    os: "Rocky Linux 8"
    status: active
    services:
      - name: docker
        startup_cmd: "systemctl start docker"
        shutdown_cmd: "systemctl stop docker"
        containers:
          - container: "active-comparable-1"
          - container: "active-comparable-2"
          - container: "portainer_edge_agent"   
          - container: "nginx-proxy-manager"  
  - id: 39
    hostname: ovh40
    dns: ovh40.fangintel.com
    provider: ovh
    region: ca-east-bhs-a
    location: "North America (Canada – East – Beauharnois)"
    instance_type: "6c/12t 64GB"
    ip: "************"
    os: "Rocky Linux 8"
    status: active
    services:
      - name: docker
        startup_cmd: "systemctl start docker"
        shutdown_cmd: "systemctl stop docker"
        containers:
          - container: "minio-mc"
          - container: "portainer_edge_agent"   
          - container: "nginx-proxy-manager"  
  - id: 40
    hostname: ovh41
    dns: ovh41.fangintel.com
    provider: ovh
    region: ca-east-bhs-a
    location: "North America (Canada – East – Beauharnois)"
    instance_type: "24c/48t 128GB"
    ip: "************"
    os: "Rocky Linux 8"
    status: active
    services:
      - name: docker
        startup_cmd: "systemctl start docker"
        shutdown_cmd: "systemctl stop docker"
        containers:
          - container: "recsys-cangen-ranker"
          - container: "portainer_edge_agent"   
          - container: "nginx-proxy-manager"  
  - id: 41
    hostname: ovh42
    dns: ovh42.fangintel.com
    provider: ovh
    region: ca-east-bhs-a
    location: "North America (Canada – East – Beauharnois)"
    instance_type: "8c/16t 128GB"
    ip: "**************"
    os: "Rocky Linux 8"
    status: active
    services:
      - name: docker
        startup_cmd: "systemctl start docker"
        shutdown_cmd: "systemctl stop docker"
        containers:
          - container: "notebook"
          - container: "portainer_edge_agent"   
          - container: "minio-mc" 
  - id: 42
    hostname: ovh81
    dns: ovh81.fangintel.com
    provider: ovh
    region: ca-east-bhs-a
    location: "North America (Canada – East – Beauharnois)"
    instance_type: "4c/8t 32GB"
    ip: "*************"
    os: "Rocky Linux 8"
    status: active
    services:
    description: "reed服务器"  
  - id: 43
    hostname: ovh101
    dns: ovh101.fangintel.com
    provider: ovh
    region: ca-east-bhs-a
    location: "North America (Canada – East – Beauharnois)"
    instance_type: "6c/12t 64GB"
    ip: "*************"
    os: "Rocky Linux 8"
    status: active
    services:
      - name: nginx
        startup_cmd: "systemctl start nginx"
        shutdown_cmd: "systemctl stop nginx"
        domains:
          - domain: "cache07.housesigma.com"
          - domain: "cache08.housesigma.com"
      - name: php
        description: "php"
        startup_cmd: "systemctl start php-fpm"
        shutdown_cmd: "systemctl stop php-fpm"                       
      - name: elasticsearch
        startup_cmd: "systemctl start elasticsearch"
        shutdown_cmd: "systemctl stop elasticsearch"                 
      - name: node_exporter
        startup_cmd: "systemctl start node_exporter"
        shutdown_cmd: "systemctl stop node_exporter" 
      - name: vector
        startup_cmd: "systemctl start vector"
        shutdown_cmd: "systemctl stop vector"  
  - id: 44
    hostname: ovh102
    dns: ovh102.fangintel.com
    provider: ovh
    region: ca-east-bhs-a
    location: "North America (Canada – East – Beauharnois)"
    instance_type: "24c/48t 128GB"
    ip: "*************"
    os: "Rocky Linux 8"
    status: active
    services:
      - name: nginx
        startup_cmd: "systemctl start nginx"
        shutdown_cmd: "systemctl stop nginx"
        domains:                      
      - name: elasticsearch
        startup_cmd: "systemctl start elasticsearch"
        shutdown_cmd: "systemctl stop elasticsearch"                 
      - name: node_exporter
        startup_cmd: "systemctl start node_exporter"
        shutdown_cmd: "systemctl stop node_exporter" 
  - id: 45
    hostname: ovh103
    dns: ovh103.fangintel.com
    provider: ovh
    region: ca-east-bhs-a
    location: "North America (Canada – East – Beauharnois)"
    instance_type: "6c/12t 32GB"
    ip: "*************"
    os: "Rocky Linux 8"
    status: active
    services:
      - name: nginx
        startup_cmd: "systemctl start nginx"
        shutdown_cmd: "systemctl stop nginx"
        domains:                      
      - name: elasticsearch
        startup_cmd: "systemctl start elasticsearch"
        shutdown_cmd: "systemctl stop elasticsearch"                 
      - name: node_exporter
        startup_cmd: "systemctl start node_exporter"
        shutdown_cmd: "systemctl stop node_exporter"
  - id: 46
    hostname: web01
    dns: web01.fangintel.com
    provider: digitalocean
    region: tor1
    location: "Toronto"
    instance_type: "4t 8GB"
    ip: "**************"
    os: "centos8"
    status: active
    services:
      - name: nginx
        startup_cmd: "systemctl start nginx"
        shutdown_cmd: "systemctl stop nginx"
        domains: 
          - domain: "housesigma.com"
          - domain: "web01.housesigma.com"
          - domain: "link.housesigma.com"                    
      - name: php
        description: "php"
        startup_cmd: "systemctl start php-fpm"
        shutdown_cmd: "systemctl stop php-fpm"                                      
      - name: node_exporter
        startup_cmd: "systemctl start node_exporter"
        shutdown_cmd: "systemctl stop node_exporter" 
      - name: vector
        startup_cmd: "systemctl start vector"
        shutdown_cmd: "systemctl stop vector" 
  - id: 47
    hostname: web02
    dns: web02.fangintel.com
    provider: digitalocean
    region: tor1
    location: "Toronto"
    instance_type: "4t 8GB"
    ip: "**************"
    os: "centos8"
    status: active
    description: "备用" 
    services:
      - name: nginx
        startup_cmd: "systemctl start nginx"
        shutdown_cmd: "systemctl stop nginx"
        domains:                    
      - name: php
        description: "php"
        startup_cmd: "systemctl start php-fpm"
        shutdown_cmd: "systemctl stop php-fpm"                                      
      - name: node_exporter
        startup_cmd: "systemctl start node_exporter"
        shutdown_cmd: "systemctl stop node_exporter" 
      - name: haproxy
        startup_cmd: "systemctl start haproxy"
        shutdown_cmd: "systemctl stop haproxy"
  - id: 48
    hostname: dovps02
    dns: dovps02.fangintel.com
    provider: digitalocean
    region: tor1
    location: "Toronto"
    instance_type: "4t 8GB"
    ip: "***************, **************"
    os: "centos7"
    status: active 
    services:
      - name: nginx
        startup_cmd: "systemctl start nginx"
        shutdown_cmd: "systemctl stop nginx"
        domains: 
          - domain: "static.housesigma.com" 
          - domain: "dev-a0e5d8.housesigma.com"                  
      - name: php
        description: "php"
        startup_cmd: "systemctl start php-fpm"
        shutdown_cmd: "systemctl stop php-fpm"                                      
      - name: node_exporter
        startup_cmd: "systemctl start node_exporter"
        shutdown_cmd: "systemctl stop node_exporter" 
      - name: haproxy
        startup_cmd: "systemctl start haproxy"
        shutdown_cmd: "systemctl stop haproxy"
      - name: fail2ban
        startup_cmd: "systemctl start fail2ban"
        shutdown_cmd: "systemctl stop fail2ban"       
      - name: pm2
        cmd: pm2
        apps:
          - app: "rendertron-desktop"
          - app: "rendertron-mobile"
      - name: mongo
        startup_cmd: "systemctl start mongod"
        shutdown_cmd: "systemctl stop mongod" 
      - name: redis
        startup_cmd: "systemctl start redis"
        shutdown_cmd: "systemctl stop redis" 
  - id: 49
    hostname: agents
    dns: agent.internal.housesigma.com
    provider: digitalocean
    region: tor1
    location: "Toronto"
    instance_type: "4t 8GB"
    ip: "**************"
    os: "centos8"
    status: active 
    services:
      - name: nginx
        startup_cmd: "systemctl start nginx"
        shutdown_cmd: "systemctl stop nginx"
        domains: 
          - domain: "agent.external.housesigma.com"
          - domain: "usdev.external.housesigma.com"
          - domain: "agent.internal.housesigma.com" 
          - domain: "usdev.internal.housesigma.com"                   
      - name: php
        description: "php"
        startup_cmd: "systemctl start php-fpm"
        shutdown_cmd: "systemctl stop php-fpm"                                      
      - name: node_exporter
        startup_cmd: "systemctl start node_exporter"
        shutdown_cmd: "systemctl stop node_exporter" 
      - name: vector
        startup_cmd: "systemctl start vector"
        shutdown_cmd: "systemctl stop vector" 
  - id: 50
    hostname: mongo-dovps-tor1
    dns: 
    provider: digitalocean
    region: tor1
    location: "Toronto"
    instance_type: "4t 8GB"
    ip: "**************"
    os: "centos8"
    status: active 
  - id: 51
    hostname: jobs-dovps-tor1/jobs1
    dns: jobs1.fangintel.com
    provider: digitalocean
    region: tor1
    location: "Toronto"
    instance_type: "1t 1GB"
    ip: "************, **************"
    os: "centos8"
    status: active 
    description: "WhatApp"
    services:
      - name: nginx
        startup_cmd: "systemctl start nginx"
        shutdown_cmd: "systemctl stop nginx"
        domains: 
          - domain: "jobs1.fangintel.com"                  
      - name: php
        description: "php"
        startup_cmd: "systemctl start php-fpm"
        shutdown_cmd: "systemctl stop php-fpm"                                      
      - name: node_exporter
        startup_cmd: "systemctl start node_exporter"
        shutdown_cmd: "systemctl stop node_exporter" 
      - name: supervisord
        startup_cmd: "systemctl start supervisord"
        shutdown_cmd: "systemctl stop supervisord"  
  - id: 52
    hostname: jobs2
    dns: jobs2.fangintel.com
    provider: digitalocean
    region: tor1
    location: "Toronto"
    instance_type: "1t 1GB"
    ip: "*************"
    os: "centos8"
    status: active 
    services:
      - name: jobs
        startup_cmd: "screen -r 3174"
  - id: 53
    hostname: jobs4/sf1
    dns: jobs4.fangintel.com
    provider: digitalocean
    region: sf02
    location: "SanFrancisco"
    instance_type: "4t 8GB"
    ip: "*************"
    os: "centos8"
    status: active 
    description: "WhatApp"
    services:
      - name: nginx
        startup_cmd: "systemctl start nginx"
        shutdown_cmd: "systemctl stop nginx"
        domains: 
          - domain: "map.housesigma.com"  
          - domain: "mapwest.fangintel.com" 
          - domain: "team032.fangintel.com" 
            description: "passbolt"            
      - name: php
        description: "php"
        startup_cmd: "systemctl start php-fpm"
        shutdown_cmd: "systemctl stop php-fpm"                                      
      - name: node_exporter
        startup_cmd: "systemctl start node_exporter"
        shutdown_cmd: "systemctl stop node_exporter" 
      - name: supervisord
        startup_cmd: "systemctl start supervisord"
        shutdown_cmd: "systemctl stop supervisord"
      - name: crontab
        startup_cmd: "systemctl start crond"
        shutdown_cmd: "systemctl stop crond" 
  - id: 54
    hostname: sf2
    dns: sf2.fangintel.com
    provider: digitalocean
    region: sf02
    location: "SanFrancisco"
    instance_type: "4t 8GB"
    ip: "***************"
    os: "centos8"
    status: active 
    services:
      - name: nginx
        startup_cmd: "systemctl start nginx"
        shutdown_cmd: "systemctl stop nginx"
        domains: 
          - domain: "tick2.fangintel.com"                                               
      - name: node_exporter
        startup_cmd: "systemctl start node_exporter"
        shutdown_cmd: "systemctl stop node_exporter" 
  - id: 55
    hostname: sf3
    dns: sf3.fangintel.com
    provider: digitalocean
    region: sf03
    location: "SanFrancisco"
    instance_type: "4t 8GB"
    ip: "***************"
    os: "centos8"
    status: active 
    services:
      - name: nginx
        startup_cmd: "systemctl start nginx"
        shutdown_cmd: "systemctl stop nginx"
        domains: 
          - domain: "sentry2.fangintel.com"                                               
      - name: node_exporter
        startup_cmd: "systemctl start node_exporter"
        shutdown_cmd: "systemctl stop node_exporter"
      - name: docker
        startup_cmd: "systemctl start docker"
        shutdown_cmd: "systemctl stop docker"
        containers:
          - container: "sentry-worker-1"
          - container: "sentry-cron"
          - container: "sentry" 
          - container: "sentry-postgres" 
          - container: "sentry-redis"  
  - id: 56
    hostname: sf10
    dns: sf10.fangintel.com
    provider: digitalocean
    region: sf02
    location: "SanFrancisco"
    instance_type: "1t 2GB"
    ip: "***************"
    os: "Rocky Linux 8"
    status: active 
    services:
      - name: nginx
        startup_cmd: "systemctl start nginx"
        shutdown_cmd: "systemctl stop nginx"
        domains: 
          - domain: "cache-w10.housesigma.com"                                          
      - name: node_exporter
        startup_cmd: "systemctl start node_exporter"
        shutdown_cmd: "systemctl stop node_exporter"
      - name: vector
        startup_cmd: "systemctl start vector"
        shutdown_cmd: "systemctl stop vector"  
  - id: 57
    hostname: sf11
    dns: sf11.fangintel.com
    provider: digitalocean
    region: sf02
    location: "SanFrancisco"
    instance_type: "1t 2GB"
    ip: "**************"
    os: "Rocky Linux 8"
    status: active 
    services:
      - name: nginx
        startup_cmd: "systemctl start nginx"
        shutdown_cmd: "systemctl stop nginx"
        domains: 
          - domain: "cache-w11.housesigma.com"                                          
      - name: node_exporter
        startup_cmd: "systemctl start node_exporter"
        shutdown_cmd: "systemctl stop node_exporter"
      - name: vector
        startup_cmd: "systemctl start vector"
        shutdown_cmd: "systemctl stop vector"
  - id: 58
    hostname: sf12
    dns: sf12.fangintel.com
    provider: digitalocean
    region: sf02
    location: "SanFrancisco"
    instance_type: "1t 2GB"
    ip: "*************"
    os: "Rocky Linux 8"
    status: active 
    services:
      - name: nginx
        startup_cmd: "systemctl start nginx"
        shutdown_cmd: "systemctl stop nginx"
        domains: 
          - domain: "cache-w12.housesigma.com"                                          
      - name: node_exporter
        startup_cmd: "systemctl start node_exporter"
        shutdown_cmd: "systemctl stop node_exporter"
      - name: vector
        startup_cmd: "systemctl start vector"
        shutdown_cmd: "systemctl stop vector"  
  - id: 59
    hostname: sf13
    dns: sf13.fangintel.com
    provider: digitalocean
    region: sf02
    location: "SanFrancisco"
    instance_type: "1t 2GB"
    ip: "**************"
    os: "Rocky Linux 8"
    status: active 
    services:
      - name: nginx
        startup_cmd: "systemctl start nginx"
        shutdown_cmd: "systemctl stop nginx"
        domains: 
          - domain: "cache-w13.housesigma.com"                                          
      - name: node_exporter
        startup_cmd: "systemctl start node_exporter"
        shutdown_cmd: "systemctl stop node_exporter"
      - name: vector
        startup_cmd: "systemctl start vector"
        shutdown_cmd: "systemctl stop vector"
  - id: 60
    hostname: sf14
    dns: sf14.fangintel.com
    provider: digitalocean
    region: sf02
    location: "SanFrancisco"
    instance_type: "1t 2GB"
    ip: "*************"
    os: "Rocky Linux 8"
    status: active 
    services:
      - name: nginx
        startup_cmd: "systemctl start nginx"
        shutdown_cmd: "systemctl stop nginx"
        domains: 
          - domain: "cache-w14.housesigma.com"                                          
      - name: node_exporter
        startup_cmd: "systemctl start node_exporter"
        shutdown_cmd: "systemctl stop node_exporter"
      - name: vector
        startup_cmd: "systemctl start vector"
        shutdown_cmd: "systemctl stop vector" 
  - id: 61
    hostname: sf15
    dns: sf15.fangintel.com
    provider: digitalocean
    region: sf02
    location: "SanFrancisco"
    instance_type: "1t 2GB"
    ip: "*************"
    os: "Rocky Linux 8"
    status: active 
    services:
      - name: nginx
        startup_cmd: "systemctl start nginx"
        shutdown_cmd: "systemctl stop nginx"
        domains: 
          - domain: "cache-w15.housesigma.com"                                          
      - name: node_exporter
        startup_cmd: "systemctl start node_exporter"
        shutdown_cmd: "systemctl stop node_exporter"
      - name: vector
        startup_cmd: "systemctl start vector"
        shutdown_cmd: "systemctl stop vector" 
  - id: 62
    hostname: tor11
    dns: tor11.fangintel.com
    provider: digitalocean
    region: tor01
    location: "Toronto"
    instance_type: "2t 4GB"
    ip: "*************"
    os: "Rocky Linux 8"
    status: active 
    services:
      - name: nginx
        startup_cmd: "systemctl start nginx"
        shutdown_cmd: "systemctl stop nginx"
        domains: 
          - domain: "cache-e11.housesigma.com"                                          
      - name: node_exporter
        startup_cmd: "systemctl start node_exporter"
        shutdown_cmd: "systemctl stop node_exporter"
      - name: vector
        startup_cmd: "systemctl start vector"
        shutdown_cmd: "systemctl stop vector"
  - id: 63
    hostname: tor12
    dns: tor12.fangintel.com
    provider: digitalocean
    region: tor01
    location: "Toronto"
    instance_type: "2t 4GB"
    ip: "**************"
    os: "Rocky Linux 8"
    status: active 
    services:
      - name: nginx
        startup_cmd: "systemctl start nginx"
        shutdown_cmd: "systemctl stop nginx"
        domains: 
          - domain: "cache-e12.housesigma.com"                                          
      - name: node_exporter
        startup_cmd: "systemctl start node_exporter"
        shutdown_cmd: "systemctl stop node_exporter"
      - name: vector
        startup_cmd: "systemctl start vector"
        shutdown_cmd: "systemctl stop vector"
  - id: 64
    hostname: tor13
    dns: tor13.fangintel.com
    provider: digitalocean
    region: tor01
    location: "Toronto"
    instance_type: "2t 4GB"
    ip: "**************"
    os: "Rocky Linux 8"
    status: active 
    services:
      - name: nginx
        startup_cmd: "systemctl start nginx"
        shutdown_cmd: "systemctl stop nginx"
        domains: 
          - domain: "cache-e13.housesigma.com"                                          
      - name: node_exporter
        startup_cmd: "systemctl start node_exporter"
        shutdown_cmd: "systemctl stop node_exporter"
      - name: vector
        startup_cmd: "systemctl start vector"
        shutdown_cmd: "systemctl stop vector"
  - id: 65
    hostname: tor14
    dns: tor14.fangintel.com
    provider: digitalocean
    region: tor01
    location: "Toronto"
    instance_type: "2t 4GB"
    ip: "*************"
    os: "Rocky Linux 8"
    status: active 
    services:
      - name: nginx
        startup_cmd: "systemctl start nginx"
        shutdown_cmd: "systemctl stop nginx"
        domains: 
          - domain: "cache-e14.housesigma.com"                                          
      - name: node_exporter
        startup_cmd: "systemctl start node_exporter"
        shutdown_cmd: "systemctl stop node_exporter"
      - name: vector
        startup_cmd: "systemctl start vector"
        shutdown_cmd: "systemctl stop vector"
  - id: 66
    hostname: tor15
    dns: tor15.fangintel.com
    provider: digitalocean
    region: tor01
    location: "Toronto"
    instance_type: "2t 4GB"
    ip: "**************"
    os: "Rocky Linux 8"
    status: active 
    services:
      - name: nginx
        startup_cmd: "systemctl start nginx"
        shutdown_cmd: "systemctl stop nginx"
        domains: 
          - domain: "mapeast.fangintel.com"                                          
      - name: node_exporter
        startup_cmd: "systemctl start node_exporter"
        shutdown_cmd: "systemctl stop node_exporter"
      - name: vector
        startup_cmd: "systemctl start vector"
        shutdown_cmd: "systemctl stop vector"  
  - id: 67
    hostname: tor16
    dns: tor16.fangintel.com
    provider: digitalocean
    region: tor01
    location: "Toronto"
    instance_type: "2t 4GB"
    ip: "**************"
    os: "Rocky Linux 8"
    status: active 
    services:
      - name: nginx
        startup_cmd: "systemctl start nginx"
        shutdown_cmd: "systemctl stop nginx"
        domains: 
          - domain: "cache-e15.housesigma.com"                                          
      - name: node_exporter
        startup_cmd: "systemctl start node_exporter"
        shutdown_cmd: "systemctl stop node_exporter"
      - name: vector
        startup_cmd: "systemctl start vector"
        shutdown_cmd: "systemctl stop vector"
  - id: 68
    hostname: tor17
    dns: tor17.fangintel.com
    provider: digitalocean
    region: tor01
    location: "Toronto"
    instance_type: "2t 4GB"
    ip: "***************"
    os: "Rocky Linux 8"
    status: active 
    services:
      - name: nginx
        startup_cmd: "systemctl start nginx"
        shutdown_cmd: "systemctl stop nginx"
        domains: 
          - domain: "cache-e16.housesigma.com"                                          
      - name: node_exporter
        startup_cmd: "systemctl start node_exporter"
        shutdown_cmd: "systemctl stop node_exporter"
      - name: vector
        startup_cmd: "systemctl start vector"
        shutdown_cmd: "systemctl stop vector"
  - id: 69
    hostname: tor18
    dns: tor18.fangintel.com
    provider: digitalocean
    region: tor01
    location: "Toronto"
    instance_type: "2t 4GB"
    ip: "***************"
    os: "Rocky Linux 8"
    status: active 
    services:
      - name: nginx
        startup_cmd: "systemctl start nginx"
        shutdown_cmd: "systemctl stop nginx"
        domains: 
          - domain: "cache-e17.housesigma.com"                                          
      - name: node_exporter
        startup_cmd: "systemctl start node_exporter"
        shutdown_cmd: "systemctl stop node_exporter"
      - name: vector
        startup_cmd: "systemctl start vector"
        shutdown_cmd: "systemctl stop vector"
  - id: 70
    hostname: tor19
    dns: tor19.fangintel.com
    provider: digitalocean
    region: tor01
    location: "Toronto"
    instance_type: "2t 4GB"
    ip: "**************"
    os: "Rocky Linux 8"
    status: active 
    services:
      - name: nginx
        startup_cmd: "systemctl start nginx"
        shutdown_cmd: "systemctl stop nginx"
        domains: 
          - domain: "cache-e18.housesigma.com"                                          
      - name: node_exporter
        startup_cmd: "systemctl start node_exporter"
        shutdown_cmd: "systemctl stop node_exporter"
      - name: vector
        startup_cmd: "systemctl start vector"
        shutdown_cmd: "systemctl stop vector"  
  - id: 71
    hostname: tor-etl
    dns: tor-etl.fangintel.com
    provider: digitalocean
    region: tor01
    location: "Toronto"
    instance_type: "1t 2GB"
    ip: "************4"
    os: "Rocky Linux 8"
    status: active 
    description: "herbert"
  - id: 72
    hostname: dtor10
    dns: dtor10.fangintel.com
    provider: digitalocean
    region: tor01
    location: "Toronto"
    instance_type: "2t 4GB"
    ip: "**************"
    os: "Rocky Linux 8"
    status: active 
    services:
      - name: nginx
        startup_cmd: "systemctl start nginx"
        shutdown_cmd: "systemctl stop nginx"
        domains: 
          - domain: "team.housesigma.com" 
          - domain: "teamtest.housesigma.com"                                          
      - name: node_exporter
        startup_cmd: "systemctl start node_exporter"
        shutdown_cmd: "systemctl stop node_exporter"
  - id: 73
    hostname: hk20
    dns: hk20.fangintel.com
    provider: oneprovider
    region: hk
    location: "Hong Kong"
    instance_type: "8t 32GB"
    ip: "**************"
    os: "Rocky Linux 8"
    status: active 
    services:
      - name: nginx
        startup_cmd: "systemctl start nginx"
        shutdown_cmd: "systemctl stop nginx"  
        domains:                                      
      - name: php
        description: "php"
        startup_cmd: "systemctl start php-fpm"
        shutdown_cmd: "systemctl stop php-fpm"
      - name: elasticsearch
        startup_cmd: "systemctl start elasticsearch"
        shutdown_cmd: "systemctl stop elasticsearch"
      - name: beanstalkd
        startup_cmd: "systemctl start beanstalkd"
        shutdown_cmd: "systemctl stop beanstalkd" 
      - name: mysql
        startup_cmd: "systemctl start mysql"
        shutdown_cmd: "systemctl stop mysql"
      - name: mongo
        startup_cmd: "systemctl start mongod"
        shutdown_cmd: "systemctl stop mongod"
      - name: mongo
        startup_cmd: "systemctl start mongod-session"
        shutdown_cmd: "systemctl stop mongod-session"
      - name: node_exporter
        startup_cmd: "systemctl start node_exporter"
        shutdown_cmd: "systemctl stop node_exporter"
  - id: 74
    hostname: hk22
    dns: hk22.fangintel.com
    provider: oneprovider
    region: hk
    location: "Hong Kong"
    instance_type: "12t 64GB"
    ip: "***********"
    os: "Rocky Linux 8"
    status: active 
    description: "ml test"
  - id: 75
    hostname: hk23
    dns: hk23.fangintel.com
    provider: oneprovider
    region: hk
    location: "Hong Kong"
    instance_type: "8t 32GB"
    ip: "*************"
    os: "Rocky Linux 8"
    status: active 
    description: "test"
  - id: 76
    hostname: hk06
    dns: hk06.fangintel.com
    provider: ali-intl
    region: hk
    location: "Hong Kong"
    instance_type: "2t 8GB"
    ip: "*************"
    os: "Rocky Linux 8"
    status: active 
    services:
      - name: nginx
        startup_cmd: "systemctl start nginx"
        shutdown_cmd: "systemctl stop nginx"
        domains: 
          - domain: "test-3p31a3.housesigma.com"                                          
      - name: node_exporter
        startup_cmd: "systemctl start node_exporter"
        shutdown_cmd: "systemctl stop node_exporter"
  - id: 77
    hostname: hk04
    dns: hk04.fangintel.com
    provider: ali-intl
    region: hk
    location: "Hong Kong"
    instance_type: "2t 8GB"
    ip: "**************"
    os: "Rocky Linux 8"
    status: active 
    services:
      - name: nginx
        startup_cmd: "systemctl start nginx"
        shutdown_cmd: "systemctl stop nginx"
        domains: 
          - domain: "hk.housesigma.com"                                          
      - name: node_exporter
        startup_cmd: "systemctl start node_exporter"
        shutdown_cmd: "systemctl stop node_exporter"
  - id: 78
    hostname: bhs4-etl
    dns: 
    provider: 
    region: 
    location: 
    instance_type: "8t 32GB"
    ip: "*************"
    os: "Rocky Linux "
    status: active 
    description: "spin2"
  - id: 79
    hostname: hk04
    dns: hk04.fangintel.com
    provider: ali-intl
    region: hk
    location: "Hong Kong"
    instance_type: "2t 8GB"
    ip: "**************"
    os: "Rocky Linux 8"
    status: active 
    services:
      - name: nginx
        startup_cmd: "systemctl start nginx"
        shutdown_cmd: "systemctl stop nginx"
        domains: 
          - domain: "hk.housesigma.com"                                          
      - name: node_exporter
        startup_cmd: "systemctl start node_exporter"
        shutdown_cmd: "systemctl stop node_exporter"  
  - id: 80
    hostname: eks-user-data
    dns: 
    provider: aws
    region: ca-central-1d
    location: "Canada"
    instance_type: "2t 4GB"
    ip: "***********"
    os: "AmazonLinux"
    status: active 
    services:
      - name: realagent-service
  - id: 81
    hostname: eks-user-data
    dns: 
    provider: aws
    region: ca-central-1d
    location: "Canada"
    instance_type: "2t 4GB"
    ip: "************"
    os: "AmazonLinux"
    status: active 
    services:
      - name: realagent-service
  - id: 82
    hostname: eks-user-data
    dns: 
    provider: aws
    region: ca-central-1d
    location: "Canada"
    instance_type: "2t 4GB"
    ip: "***********"
    os: "AmazonLinux"
    status: active 
    services:
      - name: realagent-service
  - id: 83
    hostname: eks-airflow-pg
    dns: 
    provider: aws
    region: ca-central-1d
    location: "Canada"
    instance_type: "4t 8GB"
    ip: "*************"
    os: "AmazonLinux"
    status: active 
    services:
      - name: eks-airflow  
  - id: 84
    hostname: eks-airflow
    dns: 
    provider: aws
    region: ca-central-1d
    location: "Canada"
    instance_type: "2t 8GB"
    ip: "************"
    os: "AmazonLinux"
    status: active 
    services:
      - name: eks-airflow 
  - id: 85
    hostname: eks-airflow
    dns: 
    provider: aws
    region: ca-central-1a
    location: "Canada"
    instance_type: "2t 8GB"
    ip: "***********"
    os: "AmazonLinux"
    status: active 
    services:
      - name: eks-airflow       
  - id: 86
    hostname: eks-airflow-public
    dns: 
    provider: aws
    region: ca-central-1b
    location: "Canada"
    instance_type: "1t 1GB"
    ip: "***********"
    os: "AmazonLinux"
    status: active 
    services:
      - name: eks-airflow   
  - id: 87
    hostname: jumpserver
    dns: 
    provider: aws
    region: ca-central-1b
    location: "Canada"
    instance_type: "1t 1GB"
    ip: "************"
    os: "AmazonLinux"
    status: active 
    services:
      - name: redshift jumpserver 
  - id: 88
    hostname: jumpserver
    dns: 
    provider: aws
    region: ca-central-1b
    location: "Canada"
    instance_type: "1t 1GB"
    ip: "************"
    os: "AmazonLinux"
    status: active 
    services:
      - name: redshift jumpserver
  - id: 89
    hostname: ca-jumpserver
    dns: 
    provider: aws
    region: ca-central-1d
    location: "Canada"
    instance_type: "2t 8GB"
    ip: "**************"
    os: "AmazonLinux"
    status: active 
    services:
      - name: jumpserver    
  - id: 90
    hostname: llama-worker-1-terraform
    dns: 
    provider: aws
    region: ca-central-1a
    location: "Canada"
    instance_type: "4t 16GB"
    ip: "*************"
    os: "AmazonLinux"
    status: active 
    services:
      - name: llama
  - id: 91
    hostname: ops-server-1
    dns: 
    provider: aws
    region: ca-central-1a
    location: "Canada"
    instance_type: "8t 32GB"
    ip: "**********"
    os: "AmazonLinux"
    status: active 
    services:
      - name: ops-server
               