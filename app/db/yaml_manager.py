import logging
from pathlib import Path
import yaml
from app.core.config import settings
from app.schemas.server import ServerCreate, Server

class YAMLManager:
    def __init__(self):
        self._data = None
        self.file_path = Path(settings.YAML_DB_PATH)

    @property
    def data(self):
        if self._data is None:
            self.load()
        return self._data

    def load(self):
        if not self.file_path.exists():
            logging.warning(f"File {self.file_path} not found!")
            self._data = {"servers": []}
            return
        with open(self.file_path, "r") as f:
            self._data = yaml.safe_load(f) or {"servers": []}
            logging.info(f"loaded {self._data["servers"].__len__()} servers")

    def save(self):
        with open(self.file_path, "w") as f:
            yaml.dump(self._data, f, sort_keys=False)

    @property
    def servers(self):
        try:
            return [Server(**s) for s in self.data["servers"]]
        except (<PERSON><PERSON><PERSON><PERSON>, ValueError) as e:
            logging.error(f"Error parsing servers data: {e}")
            return []

    def add_server(self, server: ServerCreate):
        new_id = max(s["id"] for s in self.data["servers"]) + 1 if self.data["servers"] else 1
        server_data = server.dict()
        server_data["id"] = new_id
        self.data["servers"].append(server_data)
        self.save()
        return Server(**server_data)
