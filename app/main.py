from contextlib import asynccontextmanager
from app.utils.log_buffer import setup_local_logging
from app.utils.log_uploader import LogUploader
from app.core.config import settings
from fastapi import FastAPI

from app.db.yaml_manager import YAMLManager
from app.routes.v1 import servers
from app.routes.v1 import alertmanager
from app.routes.v1 import sli


@asynccontextmanager
async def lifespan(app: FastAPI):
    # Startup
    logger = setup_local_logging()
    uploader = LogUploader(bucket_name=settings.S3_BUCKET_NAME, base_key=settings.S3_LOG_KEY)
    uploader.start()

    # 初始化时自动加载YAML数据
    YAMLManager().load()

    yield

    # Shutdown (如果需要清理资源)
    # 这里可以添加关闭时的清理逻辑


app = FastAPI(lifespan=lifespan)

app.include_router(servers.router)
app.include_router(alertmanager.router)
app.include_router(sli.router)

@app.get("/")
def health_check():
    return {"status": "OK"}
