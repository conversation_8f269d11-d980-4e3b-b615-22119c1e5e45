from pydantic import BaseModel
from typing import List, Optional

class Service(BaseModel):
    name: str
    description: Optional[str] = None
    startup_cmd: str
    shutdown_cmd: str
    domains: Optional[List[dict]] = None

class ServerBase(BaseModel):
    hostname: str
    dns: str
    provider: str
    region: str
    location: str
    instance_type: str
    ip: str
    os: str
    status: str = "active"
    services: Optional[List[Service]] = []

class ServerCreate(ServerBase):
    pass

class Server(ServerBase):
    id: int
