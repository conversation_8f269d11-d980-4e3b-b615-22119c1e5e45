import os
import time
import threading
import boto3
import gzip
import shutil
from botocore.exceptions import NoCredentialsError
from app.core.config import settings


LOG_FILE_PATH = '/app/app.log'

MAX_FILE_SIZE_MB = 200 
MAX_INTERVAL_SECONDS = 86400  
CHECK_INTERVAL_SECONDS = 3600  
MAX_RETRIES = 3  
RETRY_INTERVAL_SECONDS = 5  

class LogUploader(threading.Thread):
    def __init__(self, bucket_name: str, base_key: str):
        super().__init__()
        self.bucket_name = bucket_name
        self.base_key = base_key
        self.s3_client = boto3.client(
            's3',
            region_name=settings.AWS_REGION,
            aws_access_key_id=settings.AWS_ACCESS_KEY_ID,
            aws_secret_access_key=settings.AWS_SECRET_ACCESS_KEY,
        )
        self.last_upload_time = time.time()
        self.daemon = True  

    def run(self):
        while True:
            try:
                if self.should_upload():
                    self.upload_log_with_retry()
            except Exception as e:
                print(f"LogUploader error: {e}")
            time.sleep(CHECK_INTERVAL_SECONDS)

    def should_upload(self) -> bool:
        """
        Check if the file needs to be uploaded.
        """
        if not os.path.exists(LOG_FILE_PATH):
            return False

        file_size_mb = os.path.getsize(LOG_FILE_PATH) / (1024 * 1024)
        time_since_last_upload = time.time() - self.last_upload_time

        return file_size_mb >= MAX_FILE_SIZE_MB or time_since_last_upload >= MAX_INTERVAL_SECONDS

    def upload_log_with_retry(self):
        """
        Try to upload the log file with retries.
        """
        for attempt in range(1, MAX_RETRIES + 1):
            success = self.upload_log()
            if success:
                return
            else:
                print(f"Upload attempt {attempt} failed, retrying in {RETRY_INTERVAL_SECONDS} seconds...")
                time.sleep(RETRY_INTERVAL_SECONDS)

        print(f"Failed to upload log after {MAX_RETRIES} attempts.")

    def upload_log(self) -> bool:
        """
        Gzip compress and upload the log file to S3. Return True if success, False otherwise.
        """
        if not os.path.exists(LOG_FILE_PATH):
            return True

        try:
           
            gzipped_log_path = f"{LOG_FILE_PATH}.gz"
            with open(LOG_FILE_PATH, 'rb') as f_in:
                with gzip.open(gzipped_log_path, 'wb') as f_out:
                    shutil.copyfileobj(f_in, f_out)

            
            timestamp = time.strftime("%Y%m%d%H%M%S")
            s3_key = f"{self.base_key}/app-{timestamp}.log.gz"

           
            with open(gzipped_log_path, 'rb') as f:
                self.s3_client.put_object(
                    Bucket=self.bucket_name,
                    Key=s3_key,
                    Body=f,
                    ContentType='application/gzip'
                )

            print(f"Uploaded {s3_key} to S3 bucket {self.bucket_name}")

           
            open(LOG_FILE_PATH, 'w').close()
         
            os.remove(gzipped_log_path)

            self.last_upload_time = time.time()

            return True

        except NoCredentialsError:
            print("Error: No AWS credentials found.")
        except Exception as e:
            print(f"Error uploading log to S3: {e}")

        return False
