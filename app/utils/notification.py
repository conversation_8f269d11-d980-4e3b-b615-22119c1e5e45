import boto3
import requests
import json
import time
import logging
from sendgrid import SendGridAPIClient
from sendgrid.helpers.mail import Mail
from botocore.exceptions import BotoCoreError, NoCredentialsError
from tenacity import retry, stop_after_attempt, wait_exponential
from app.core.config import settings

logger = logging.getLogger(__name__)

# 失败重试策略（默认 5 次，指数退避）
retry_strategy = retry(stop=stop_after_attempt(5), wait=wait_exponential(multiplier=2, min=1, max=10))

class NotificationSender:

    @staticmethod
    @retry_strategy
    def send_sms_via_sns(topic_arn: str, message: str):
        """
        发送 SMS 到 AWS SNS 主题（Topic）。
        """
        try:
            logger.info(f"Initializing SNS client for region: {settings.AWS_REGION}")

            # 添加 AWS 凭证
            client = boto3.client(
                "sns",
                region_name=settings.AWS_REGION,
                aws_access_key_id=settings.AWS_ACCESS_KEY_ID,
                aws_secret_access_key=settings.AWS_SECRET_ACCESS_KEY
            )

            logger.info(f"Sending SMS via SNS: TopicArn={topic_arn}")

            alert_info = json.loads(message)
            formatted_message = f"Alert: {alert_info.get('alertname')} | " \
                                f"Status: {alert_info.get('status')} | " \
                                f"Start Time: {alert_info.get('start_at')} | " \
                                f"Level: {alert_info.get('level')} | " \
                                f"Description: {alert_info.get('description', 'No description provided.')}"
        
            response = client.publish(
                TopicArn=topic_arn,
                Message=formatted_message
            )

            logger.info(
                f"SMS sent via SNS successfully. TopicArn={topic_arn}, Message={formatted_message}, Response={response}"
            )
            return response

        except NoCredentialsError:
            logger.error("AWS Credentials not found! Please configure AWS_ACCESS_KEY_ID and AWS_SECRET_ACCESS_KEY.")
            raise

        except BotoCoreError as e:
            logger.error(f"Failed to send SMS via SNS: {e}")
            raise

    @staticmethod
    @retry_strategy
    def send_email(subject: str, body: str, recipient: str):
        """
        使用 SendGrid 发送邮件。
        """
        try:
            alert_info = json.loads(body)
            formatted_message = f"Alert: {alert_info.get('alertname')}\n" \
                                f"Status: {alert_info.get('status')}\n" \
                                f"Start Time: {alert_info.get('start_at')}\n" \
                                f"Level: {alert_info.get('level')}\n" \
                                f"Description: {alert_info.get('description', 'No description provided.')}\n"
            message = Mail(
                from_email=settings.SMTP_USERNAME,
                to_emails=recipient,
                subject=subject,
                plain_text_content=formatted_message
            )

            sg = SendGridAPIClient(settings.SMTP_PASSWORD)
            response = sg.send(message)

            if response.status_code in [200, 202]:
                logger.info(f"Email sent to {recipient} via SendGrid. Content={formatted_message}")
            else:
                logger.error(f"SendGrid email failed, status: {response.status_code}, body: {response.body}")
                raise Exception("SendGrid email failed")

        except Exception as e:
            logger.error(f"Failed to send email to {recipient} via SendGrid: {e}")
            raise

    @staticmethod
    @retry_strategy
    def send_discord_webhook(webhook_url: str, message: str):
        """
        发送 Discord Webhook 消息。
        :param webhook_url: Discord Webhook 网址（从 notification_value 传入）
        :param message: 需要发送的消息内容
        """
        try:
            if not webhook_url:
                raise ValueError("Discord Webhook URL is missing.")

            # Parse alert_info data from the message
            alert_info = json.loads(message)
            alertname = alert_info.get("alertname")
            status = alert_info.get("status")
            start_at = alert_info.get("start_at")
            level = alert_info.get("level")
            description = alert_info.get("description", "No description provided.")

            # Beautify the message using Discord embed format
            embed = {
                "title": f"🚨 **{alertname} Alert** 🚨",  # Use alertname as the title
                "description": f"**Status**: {status}\n**Start Time**: {start_at}\n**Level**: {level}\n**Description**: {description}",  # Add detailed information
                "color": 16711680 if status == "firing" else 3066993,  # Red for "firing", green for "resolved"
            }

            # Prepare the Discord message payload using embeds
            payload = {
                "embeds": [embed]  # Embed the message for better presentation
            }
            response = requests.post(webhook_url, json=payload)

            if response.status_code in [200, 204]:
                logger.info(
                    f"Message sent to Discord successfully. Webhook URL={webhook_url}, Message={json.dumps(payload)}"
                )
            else:
                logger.error(f"Failed to send Discord message, status code: {response.status_code}, response: {response.text}")
                raise Exception(f"Discord webhook failed: {response.status_code}")

        except requests.RequestException as e:
            logger.error(f"Failed to send Discord webhook to {webhook_url}: {e}")
            raise
