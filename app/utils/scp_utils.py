import os
import paramiko
import io
from app.core.config import settings

def upload_file_via_scp(local_filepath: str, remote_filepath: str):
    """
    Upload a file to the remote Prometheus server via SCP using sudo privileges.

    The file will be uploaded to a temporary path first, then moved to the target path using sudo.
    """

    filename = os.path.basename(remote_filepath)
    tmp_remote_path = f"/tmp/{filename}"

    try:
        # Establish SSH connection
        private_key_content = settings.SSH_PRIVATE_KEY
        private_key_file = io.StringIO(private_key_content)
        private_key = paramiko.RSAKey.from_private_key(private_key_file)
        ssh = paramiko.SSHClient()
        ssh.set_missing_host_key_policy(paramiko.AutoAddPolicy())
        ssh.connect(
            hostname=settings.PROMETHEUS_SERVER,
            username=settings.SSH_USERNAME,
            pkey=private_key,
        )

        # Upload the file to the temporary path
        sftp = ssh.open_sftp()
        sftp.put(local_filepath, tmp_remote_path)
        sftp.close()

        # Move the file to the target path using sudo, then remove the temporary file
        cmd = f"sudo mv {tmp_remote_path} {remote_filepath} && sudo rm -f {tmp_remote_path}"
        stdin, stdout, stderr = ssh.exec_command(cmd)
        exit_status = stdout.channel.recv_exit_status()

        if exit_status == 0:
            print(f"File uploaded and moved to {remote_filepath} successfully.")
        else:
            error_output = stderr.read().decode()
            print(f"Failed to move file via sudo: {error_output}")
            raise Exception(f"Sudo mv failed: {error_output}")

        ssh.close()

    except Exception as e:
        print(f"Error uploading file via SCP with sudo: {e}")
        raise e
