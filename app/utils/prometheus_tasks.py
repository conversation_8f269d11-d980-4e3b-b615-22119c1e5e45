import requests
from app.utils.scp_utils import upload_file_via_scp
from app.core.config import settings
import time


def reload_prometheus_config():
    """
    Reload the Prometheus configuration via HTTP request.
    """
    try:
        # Step 1: Reload Prometheus configuration
        response = requests.post(f'https://{settings.PROMETHEUS_SERVER}/prometheus/-/reload', timeout=10)
        if response.status_code == 200:
            print("Prometheus configuration successfully reloaded.")
        else:
            print(f"Failed to reload Prometheus: {response.text}")
            raise Exception(f"Failed to reload Prometheus: {response.text}")
        
        # Step 2: Wait for a while to ensure Prometheus has reloaded
        time.sleep(5)

        # Step 3: Check Prometheus health
        health_response = requests.get(f'https://{settings.PROMETHEUS_SERVER}/prometheus/-/healthy', timeout=10)
        if health_response.status_code == 200:
            print("Prometheus is healthy.")
        else:
            print(f"Prometheus health check failed: {health_response.text}")
            raise Exception(f"Prometheus health check failed: {health_response.text}")       
    except Exception as e:
        print(f"Error reloading Prometheus: {e}")
        raise e


def upload_to_prometheus(local_filepath: str, remote_filepath: str):
    """
    Upload Prometheus configuration file to the remote server and reload the Prometheus configuration.

    :param local_filepath: The local configuration file path
    :param remote_filepath: The remote configuration file path
    """
    # Upload the file to Prometheus server
    upload_file_via_scp(local_filepath, remote_filepath)
    
    # Reload Prometheus configuration
    reload_prometheus_config()
