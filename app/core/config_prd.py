import boto3
from pydantic_settings import BaseSettings
from datetime import datetime
import os


def get_ssm_param(name: str, with_decryption: bool = True) -> str:
    ssm = boto3.client("ssm", region_name="ca-central-1")
    return ssm.get_parameter(Name=name, WithDecryption=with_decryption)["Parameter"]["Value"]


class PrdSettings(BaseSettings):
    YAML_DB_PATH: str = "app/db/models/assets.yaml"
    # 其他配置项保持不变...
    DATABASE_URI: str = ""

    S3_BUCKET_NAME: str = os.getenv("S3_BUCKET_NAME", "housesigma-internal-file-share")
    S3_LOG_KEY: str = os.getenv("S3_LOG_KEY", "yy-folder/devops-gateway-logs")

    LOG_DIR: str = os.getenv("LOG_DIR", "logs")
    SLI_FILE_PATH: str = "app/db/models/sli.yaml"
    PROMETHEUS_SERVER: str = os.getenv("PROMETHEUS_SERVER", "monitoring.fangintel.com")
    SSH_USERNAME: str = os.getenv("SSH_USERNAME", "rocky")
    SSH_PRIVATE_KEY: str = get_ssm_param("/devops-gateway/prd/config/ssh_private_key")
    BASE_RULES_DIR: str = os.getenv("BASE_RULES_DIR", "app/db/models/prometheus_rules")
    REMOTE_FILE_PATH: str = os.getenv("REMOTE_FILE_PATH", "/etc/prometheus/rules/prometheus_rules.yaml")

    # AWS SNS 配置
    AWS_ACCESS_KEY_ID: str = get_ssm_param("/devops-gateway/prd/config/aws_access_key_id")
    AWS_SECRET_ACCESS_KEY: str = get_ssm_param("/devops-gateway/prd/config/aws_secret_access_key")
    AWS_REGION: str = os.getenv("AWS_REGION", "ca-central-1")

    # 邮件配置
    SMTP_USERNAME: str = get_ssm_param("/devops-gateway/prd/config/smtp_username")
    SMTP_PASSWORD: str = get_ssm_param("/devops-gateway/prd/config/smtp_password")

    # mysql数据库配置
    DB_USER: str = get_ssm_param("/devops-gateway/prd/config/db_user")
    DB_PASSWORD: str = get_ssm_param("/devops-gateway/prd/config/db_password")
    DB_HOST: str = get_ssm_param("/devops-gateway/prd/config/db_host")
    DB_PORT: str = get_ssm_param("/devops-gateway/prd/config/db_port")
    DB_NAME: str = get_ssm_param("/devops-gateway/prd/config/db_name")

    @property
    def DATABASE_URL(self) -> str:
        return f"mysql+pymysql://{self.DB_USER}:{self.DB_PASSWORD}@{self.DB_HOST}:{self.DB_PORT}/{self.DB_NAME}"

    def generate_rules_file_path(self):
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        return os.path.join(self.BASE_RULES_DIR, f"sli_rules_{timestamp}.yml")

settings = PrdSettings()
