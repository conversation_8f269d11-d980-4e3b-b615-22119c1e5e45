from fastapi import APIRouter, Depends
from sqlalchemy.orm import Session
from app.db.session import get_db
from app.schemas.alertmanager import AlertManager
from app.services.alert_processing import AlertProcessor
import logging

router = APIRouter(prefix="/alertmanager", tags=["alertmanager"])
logger = logging.getLogger(__name__)

@router.post("/webhook")
def receive_alerts(alert_manager: AlertManager, db: Session = Depends(get_db)):
    """
    接收 AlertManager 发送的报警数据，并进行处理
    """
    processor = AlertProcessor(db)

    results = []
    for alert in alert_manager.alerts:  # 遍历 AlertManager.alerts
        result = processor.process_alert(alert)
        if result:
            results.append(result)

    return {"message": "Alerts processed", "details": results}
