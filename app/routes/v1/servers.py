from fastapi import APIRouter, HTTPException
from app.schemas.server import Server, ServerCreate
from app.services.server_service import ServerService

router = APIRouter(prefix="/servers", tags=["servers"])
service = ServerService()

@router.get("/", response_model=list[Server])
def list_servers():
    return service.get_all()

@router.post("/", response_model=Server)
def create_server(server: ServerCreate):
    return service.create_server(server)

@router.get("/search")
def search_servers(
        hostname: str = None,
        service_name: str = None
):
    return service.search_servers(
        hostname=hostname,
        service_name=service_name
    )

@router.get("/{server_id}", response_model=Server)
def get_server(server_id: int):
    server = service.get_by_id(server_id)
    if not server:
        raise HTTPException(status_code=404, detail="Server not found")
    return server
