from typing import List, Optional
from app.schemas.server import Server, ServerCreate
from app.db.yaml_manager import YAMLManager

class ServerService:
    def __init__(self):
        self.yaml = YAMLManager()

    def get_all(self) -> List[Server]:
        return self.yaml.servers

    def get_by_id(self, server_id: int) -> Optional[Server]:
        return next((s for s in self.yaml.servers if s.id == server_id), None)

    def create_server(self, server: ServerCreate) -> Server:
        return self.yaml.add_server(server)

    def search_servers(self,
                       hostname: str = None,
                       service_name: str = None) -> List[Server]:
        results = self.yaml.servers
        if hostname:
            results = [s for s in results if hostname.lower() in s.hostname.lower()]
        if service_name:
            results = [s for s in results if s.services is not None and any(
                service.name.lower() == service_name.lower()
                for service in s.services
            )]
        return results
