import yaml
from typing import Dict, Any, List
from collections import defaultdict
from app.core.config import settings
from app.utils.prometheus_tasks import upload_to_prometheus

def load_sli_data() -> Dict[str, Any]:
    try:
        # Use the sli_file_path from settings to load the SLI YAML file
        with open(settings.SLI_FILE_PATH, 'r') as file:
            sli_data = yaml.safe_load(file)
        return sli_data
    except Exception as e:
        # Raise an error if the SLI data cannot be loaded
        raise Exception(f"Error loading SLI data from {settings.SLI_FILE_PATH}: {str(e)}")

def convert_to_prometheus_rules(sli_data: Dict[str, Any]) -> Dict[str, Any]:
    """
    Convert SLI data into Prometheus rules format and merge rules under the same group name.
    Updated to work with the new sli.yaml format that uses 'alert' and 'expr' fields.

    :param sli_data: The input SLI data
    :return: The converted Prometheus rules in dictionary format
    """
    group_dict: Dict[str, List[Dict[str, Any]]] = defaultdict(list)

    for category, services in sli_data.items():
        for service in services:
            group_name = f"{category}_{service['group']}"
            for sli in service.get('sli', []):
                # Extract alert name from the 'alert' field (new format)
                alert_name = sli.get('alert')
                if not alert_name:
                    # Fallback to 'name' field for backward compatibility
                    alert_name = sli.get('name')
                    if not alert_name:
                        continue  # Skip if no alert name found

                # Extract expression from 'expr' field (new format)
                metric_expr = sli.get('expr')
                if not metric_expr:
                    # Fallback to 'metric' field for backward compatibility
                    metric_expr = sli.get('metric', f"{alert_name} > 0")

                # Build the rule in Prometheus format
                rule = {
                    "alert": alert_name,
                    "expr": metric_expr,
                    "for": sli.get("for", "0m"),
                    "labels": sli.get("labels", {}),
                    "annotations": {}
                }

                # Handle annotations - check for new format first
                if "annotations" in sli:
                    rule["annotations"] = sli["annotations"]
                else:
                    # Fallback to old format for backward compatibility
                    rule["annotations"] = {
                        "summary": sli.get("summary", ""),
                        "description": sli.get("description", "")
                    }

                group_dict[group_name].append(rule)

    # 构造最终 groups 列表
    groups = [{"name": name, "rules": rules} for name, rules in group_dict.items()]

    return {"groups": groups}


def update_prometheus_rules():
    """
    Convert SLI data into Prometheus rules, save it to a file, upload it to the remote server, and reload Prometheus configuration.

    :param sli_data: The SLI data to be converted into Prometheus rules
    """
    local_filepath = settings.generate_rules_file_path()
    remote_filepath = settings.REMOTE_FILE_PATH

    # Load the SLI data from file
    sli_data = load_sli_data()

    # Convert SLI data into Prometheus rules
    prometheus_rules = convert_to_prometheus_rules(sli_data)
    
    # Write the Prometheus rules to a local file
    with open(local_filepath, 'w') as f:
        yaml.dump(prometheus_rules, f, width=1000)

    # Upload the file to Prometheus server and reload configuration
    upload_to_prometheus(local_filepath, remote_filepath)
