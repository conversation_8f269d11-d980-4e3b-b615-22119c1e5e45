import json
import logging
from datetime import datetime, timezone, timedelta
from typing import Optional, Dict, Any, List
from sqlalchemy.orm import Session
from dateutil import parser
from app.db.models.alert import Alert
from app.db.models.alert_level_rounds import AlertLevelRounds
from app.db.models.notification_config import NotificationConfig
from app.db.models.notification_logs import NotificationLog
from app.schemas.alertmanager import AlertManagerAlert
from app.utils.notification import NotificationSender

logger = logging.getLogger(__name__)

class AlertProcessor:
    def __init__(self, db: Session):
        self.db = db

    def store_alert(self, alert_data: dict) -> Optional[int]:
        try:
            start_at = parser.parse(alert_data["startsAt"]).astimezone(timezone.utc)
            alert_record = Alert(
                alertname=alert_data["labels"].get("alertname"),
                status=alert_data["status"].lower(),
                alert_level=alert_data["labels"].get("alert_level"),
                start_at=start_at,
                instance=alert_data["labels"].get("instance"),
                content=json.dumps(alert_data, default=str)
            )
            self.db.add(alert_record)
            self.db.commit()
            self.db.refresh(alert_record)
            return alert_record.id
        except Exception as e:
            self.db.rollback()
            logger.error(f"Error storing alert: {e}")
            return None

    def get_alert_round_id(self, alert: AlertManagerAlert) -> Optional[int]:
        try:
            start_at = parser.parse(alert.startsAt).astimezone(timezone.utc)
            now = datetime.now(timezone.utc)
            duration_minutes = (now - start_at).total_seconds() / 60
            alert_level = alert.labels.get("alert_level")

            rounds = (
                self.db.query(AlertLevelRounds)
                .filter_by(level_name=alert_level)
                .order_by(AlertLevelRounds.round.asc())
                .all()
            )

            matched_round = None
            for round_info in rounds:
                # If timeout_round is None, treat as permanent match (last round)
                if round_info.timeout_round is None or duration_minutes >= (round_info.timeout_round - 1):
                    matched_round = round_info
                else:
                    break
            return matched_round.id if matched_round else None
        except Exception as e:
            logger.error(f"Error calculating alert round: {e}")
            return None

    def _should_send_sms(self, duration_minutes: float, round_info: AlertLevelRounds) -> bool:
        timeout = round_info.timeout_round or 0
        frequency = round_info.frequency or 0
        error_margin = round_info.timeout_error or 0

        if frequency == 0:
            return duration_minutes >= timeout

        n = round((duration_minutes - timeout) / frequency)
        if n < 0:
            return False
        expected_time = timeout + n * frequency
        logger.info(f"Expected time: {expected_time}, Duration minutes: {duration_minutes}, Error margin: {error_margin}")
        return abs(duration_minutes - expected_time) <= error_margin

    def send_notifications(self, alert_id: int, alert: AlertManagerAlert, is_recovery: bool = False):
        alert_level = alert.labels.get("alert_level")
        status = alert.status.lower()

        alert_info = {
            "alertname": alert.labels.get("alertname"),
            "status": status,
            "level": alert.labels.get("alert_level"),
            "start_at": alert.startsAt,
            "description": alert.annotations.get("description", "No description provided.")
        }
        message = json.dumps(alert_info, indent=2)

        if is_recovery:
            alert_ids = self.find_alert_ids_by_identity(alert)
            previous_notifications = (
                self.db.query(
                    NotificationLog.notification_type,
                    NotificationLog.notification_value
                )
                .filter(
                    NotificationLog.alert_id.in_(alert_ids),
                    NotificationLog.status == "firing"
                )
                .distinct(
                    NotificationLog.notification_type,
                    NotificationLog.notification_value
                )
                .all()
            )

            if not previous_notifications:
                logger.info(f"No previous notification found for alert_id {alert_id}, skip recovery notification.")
                return

            for notification_type, notification_value in previous_notifications:
                try:
                    if notification_type == "sms":
                        NotificationSender.send_sms_via_sns(notification_value, message)
                    elif notification_type == "email":
                        NotificationSender.send_email("Alert Notification", message, notification_value)
                    elif notification_type == "discord":
                        NotificationSender.send_discord_webhook(notification_value, message)
                    else:
                        logger.warning(f"Unknown notification type: {notification_type}")
                    self.store_notification_log(alert_id, notification_type, notification_value, status)
                except Exception as e:
                    logger.error(f"Failed to send {notification_type} to {notification_value}: {e}")
        else:
            notifications = (
                self.db.query(NotificationConfig)
                .filter_by(level_name=alert_level)
                .all()
            )

            if not notifications:
                logger.info(f"No notifications configured for alert_level {alert_level}")
                return

            current_round_id = self.get_alert_round_id(alert)
            if current_round_id is None:
                logger.info("Alert round is None, skip SMS notification")
                current_round_obj = None
            else:
                current_round_obj = self.db.query(AlertLevelRounds).get(current_round_id)

            start_at = parser.parse(alert.startsAt).astimezone(timezone.utc)
            duration_minutes = (datetime.now(timezone.utc) - start_at).total_seconds() / 60

            for notification in notifications:
                notification_type = notification.notification_type
                notification_value = notification.notification_value
                logger.info({notification_type: notification_value})

                try:
                    if notification_type == "sms":
                        if not current_round_obj:
                            continue
                        target_round = self.db.query(AlertLevelRounds).get(notification.alert_level_round_id)
                        if not target_round or current_round_obj.round < target_round.round:
                            continue
                        if not self._should_send_sms(duration_minutes, current_round_obj):
                            continue
                        NotificationSender.send_sms_via_sns(notification_value, message)
                    elif notification_type == "email":
                        NotificationSender.send_email("Alert Notification", message, notification_value)
                    elif notification_type == "discord":
                        NotificationSender.send_discord_webhook(notification_value, message)
                    else:
                        logger.warning(f"Unknown notification type: {notification_type}")
                    self.store_notification_log(alert_id, notification_type, notification_value, status)
                except Exception as e:
                    logger.error(f"Failed to send {notification_type} to {notification_value}: {e}")

    def store_notification_log(self, alert_id: int, notification_type: str, notification_value: str, status: str):
        try:
            log_entry = NotificationLog(
                alert_id=alert_id,
                notification_type=notification_type,
                notification_value=notification_value,
                status=status
            )
            self.db.add(log_entry)
            self.db.commit()
        except Exception as e:
            self.db.rollback()
            logger.error(f"Error storing notification log: {e}")

    # def find_existing_alert_id(self, alert: AlertManagerAlert) -> Optional[int]:
    #     try:
    #         alertname = alert.labels.get("alertname")
    #         instance = alert.labels.get("instance")
    #         start_at = parser.parse(alert.startsAt).astimezone(timezone.utc)
    #         start_time_lower = start_at - timedelta(seconds=1)
    #         start_time_upper = start_at + timedelta(seconds=1)

    #         query = self.db.query(Alert).filter(
    #             Alert.alertname == alertname,
    #             Alert.start_at.between(start_time_lower, start_time_upper),
    #             Alert.instance == instance if instance else True
    #         )
    #         record = query.first()
    #         return record.id if record else None
    #     except Exception as e:
    #         logger.error(f"Error finding existing alert id: {e}")
    #         return None
        
    def find_alert_ids_by_identity(self, alert: AlertManagerAlert) -> List[int]:
        alertname = alert.labels.get("alertname")
        instance = alert.labels.get("instance")
        start_at = parser.parse(alert.startsAt).astimezone(timezone.utc)
        start_time_lower = start_at - timedelta(seconds=1)
        start_time_upper = start_at + timedelta(seconds=1)

        query = (
            self.db.query(Alert.id)
            .filter(
                Alert.alertname == alertname,
                Alert.start_at.between(start_time_lower, start_time_upper),
                Alert.instance == instance if instance else True
            )
        )
        return [row.id for row in query.all()]


    def process_alert(self, alert: AlertManagerAlert) -> Optional[Dict[str, Any]]:
        status = alert.status.lower()

        if status == "firing":
            alert_id = self.store_alert(alert.dict())
            if alert_id is None:
                return None
            # round_id = self.get_alert_round_id(alert)
            # if round_id is None:
            #     return None
            self.send_notifications(alert_id, alert, is_recovery=False)
            return {"alert_id": alert_id}

        elif status == "resolved":
            # alert_id = self.find_existing_alert_id(alert)
            alert_id = self.store_alert(alert.dict())
            if alert_id is None:
                return None
            self.send_notifications(alert_id, alert, is_recovery=True)
            return {"alert_id": alert_id}

        else:
            logger.warning(f"Unknown alert status: {status}")
            return None
