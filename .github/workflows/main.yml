name: deploy to eks

on:
  workflow_dispatch:
  # push:
  #   tags:
  #   - 'v[0-9]+\.[0-9]+\.[0-9]+'
  
jobs:
  build:
    name: Build Docker Image
    runs-on: [ aws, self-hosted, linux, X64, CA, OVH ]
    steps:  
        - name: clean workspace before starting job
          run: find ${{ github.workspace }} -mindepth 1 -delete

        - uses: actions/checkout@692973e3d937129bcbf40652eb9f2f61becf3332 # v4.1.7
          
        - name: Setup AWS ECR Details
          uses: aws-actions/configure-aws-credentials@v4
          with:
            aws-access-key-id: ${{ secrets.AWS_ACCESS_KEY_ID }}
            aws-secret-access-key: ${{ secrets.AWS_SECRET_ACCESS_KEY }}
            aws-region: ca-central-1

        - name: Login to Amazon ECR
          id: login-pf-aws-ecr
          uses: aws-actions/amazon-ecr-login@v2

        - name: Build and push the tagged docker image to Amazon ECR
          env:
            ECR_REGISTRY: ${{ steps.login-pf-aws-ecr.outputs.registry }}
            ECR_REPOSITORY: housesigma/devops-gateway
            IMAGE_TAG: ${{ github.sha }}
          run: |
            docker build -t $ECR_REGISTRY/$ECR_REPOSITORY:$IMAGE_TAG .
            docker push $ECR_REGISTRY/$ECR_REPOSITORY:$IMAGE_TAG

        - name: Setup kubectl 
          uses: azure/setup-kubectl@v4
          with:
            version: 'v1.30.3' 
          id: install


        - name: Update kube config
          run: aws eks update-kubeconfig --region ca-central-1 --name airflow

        - name: Deploy to EKS
          env:
            ECR_REGISTRY: ${{ steps.login-pf-aws-ecr.outputs.registry }}
            ECR_REPOSITORY: housesigma/devops-gateway
            IMAGE_TAG: ${{ github.sha }}
          run: |
            kubectl set image deployment/devops-gateway devops-gateway=$ECR_REGISTRY/$ECR_REPOSITORY:$IMAGE_TAG --namespace devops


