apiVersion: apps/v1
kind: Deployment
metadata:
  name: devops-gateway
  namespace: devops 
  labels:
    app: devops-gateway
spec:
  replicas: 1
  selector:
    matchLabels:
      app: devops-gateway
  template:
    metadata:
      labels:
        app: devops-gateway
    spec:
      serviceAccountName: devops-gateway-sa
      containers:
      - name: devops-gateway
        image: ************.dkr.ecr.ca-central-1.amazonaws.com/housesigma/devops-gateway:f928fb31079ca8001657f55ef366ad032c26b6a3 
        ports:
        - containerPort: 8000
        env:
        - name: ENV
          value: "prd"
        imagePullPolicy: Always     
      restartPolicy: Always   
---  
apiVersion: v1
kind: Service
metadata:
  name: devops-gateway
  namespace: devops
  labels:
    app: devops-gateway
spec:
  type: NodePort
  selector:
    app: devops-gateway
  ports:
    - protocol: TCP
      port: 8000          
      targetPort: 8000
      nodePort: 30000  
---
apiVersion: apps/v1
kind: Deployment
metadata:
  name: mysql
  namespace: devops
  labels:
    app: mysql
spec:
  replicas: 1
  selector:
    matchLabels:
      app: mysql
  template:
    metadata:
      labels:
        app: mysql
    spec:
      nodeSelector:
        app: airFlow    
      containers:
      - name: mysql
        image: mysql:8.4.4
        ports:
        - containerPort: 3306
        env:
        - name: MYSQL_ROOT_PASSWORD
          value: "nPk!8JWwn6"
        volumeMounts:
        - name: mysql-data
          mountPath: /var/lib/mysql
      volumes:
      - name: mysql-data
        persistentVolumeClaim:
          claimName: devops-mysql-pvc
---
apiVersion: v1
kind: Service
metadata:
  name: mysql
  namespace: devops
  labels:
    app: mysql
spec:
  type: ClusterIP
  ports:
  - port: 3306
    targetPort: 3306
  selector:
    app: mysql
---
apiVersion: v1
kind: PersistentVolume
metadata:
  name: devops-mysql-pvc
spec:
  capacity:
    storage: 20Gi
  volumeMode: Filesystem
  accessModes:
    - ReadWriteMany
  storageClassName: ""
  persistentVolumeReclaimPolicy: Retain
  csi:
    driver: efs.csi.aws.com
    volumeHandle: fs-06f79e8b0d839daa4:/devops/mysql
---
apiVersion: v1
kind: PersistentVolumeClaim
metadata:
  name: devops-mysql-pvc
  namespace: devops
spec:
  accessModes:
    - ReadWriteMany
  storageClassName: ""
  resources:
    requests:
      storage: 20Gi
